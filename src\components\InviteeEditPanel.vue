<template>
  <div class="p-6 overflow-y-auto border-l border-gray-200 h-full">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-xl font-semibold text-gray-800">
        {{ inviteeId ? 'Edit Guest' : 'Add New Guest' }}
      </h2>
      <button
        v-if="inviteeId"
        @click="emit('saved')"
        class="text-gray-500 hover:text-gray-700 p-1 rounded-md hover:bg-gray-100"
      >
        <svg
          class="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>
    </div>

    <form @submit.prevent="saveInvitee">
      <div class="mb-4">
        <label for="firstName" class="block text-sm font-medium text-gray-700">First Name</label>
        <input
          type="text"
          id="firstName"
          v-model="currentInvitee.firstName"
          class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-sm focus:ring-gray-500 focus:border-gray-500"
          required
        />
      </div>
      <div class="mb-4">
        <label for="lastName" class="block text-sm font-medium text-gray-700">Last Name</label>
        <input
          type="text"
          id="lastName"
          v-model="currentInvitee.lastName"
          class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-sm focus:ring-gray-500 focus:border-gray-500"
          required
        />
      </div>
      <div class="mb-4">
        <label for="phoneNumber" class="block text-sm font-medium text-gray-700"
          >Phone Number</label
        >
        <input
          type="text"
          id="phoneNumber"
          v-model="currentInvitee.phoneNumber"
          class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-sm focus:ring-gray-500 focus:border-gray-500"
        />
      </div>
      <div class="mb-4">
        <label for="confirmedGuests" class="block text-sm font-medium text-gray-700"
          >Confirmed Guests</label
        >
        <input
          type="number"
          id="confirmedGuests"
          v-model.number="currentInvitee.confirmedGuests"
          class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2 text-sm focus:ring-gray-500 focus:border-gray-500"
          min="0"
        />
      </div>

      <div class="mb-6">
        <h3 class="text-base font-medium text-gray-700 mb-2">Events</h3>
        <div class="flex items-center mb-2">
          <input
            type="checkbox"
            id="houseParty"
            v-model="currentInvitee.events.houseParty"
            class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500"
          />
          <label for="houseParty" class="ml-2 block text-sm text-gray-900">House Party</label>
        </div>
        <div class="flex items-center mb-2">
          <input
            type="checkbox"
            id="henna"
            v-model="currentInvitee.events.henna"
            class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500"
          />
          <label for="henna" class="ml-2 block text-sm text-gray-900">Henna</label>
        </div>
        <div class="flex items-center">
          <input
            type="checkbox"
            id="wedding"
            v-model="currentInvitee.events.wedding"
            class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500"
          />
          <label for="wedding" class="ml-2 block text-sm text-gray-900">Wedding</label>
        </div>
      </div>

      <button
        type="submit"
        class="w-full bg-gray-700 hover:bg-gray-800 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out"
      >
        {{ inviteeId ? 'Update Guest' : 'Add Guest' }}
      </button>
      <p v-if="inviteesStore.isLoading" class="text-center text-gray-600 mt-4 text-sm">Saving...</p>
      <p v-if="inviteesStore.error" class="text-center text-red-500 mt-4 text-sm">
        {{ inviteesStore.error }}
      </p>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useInviteesStore, type Invitee } from '../stores/invitees'

const props = defineProps<{
  inviteeId: string | null
}>()

const emit = defineEmits(['saved'])

const inviteesStore = useInviteesStore()

const defaultInvitee: Invitee = {
  firstName: '',
  lastName: '',
  phoneNumber: '',
  confirmedGuests: 0,
  events: {
    houseParty: false,
    henna: false,
    wedding: false,
  },
}

const currentInvitee = ref<Invitee>({ ...defaultInvitee })

watch(
  () => props.inviteeId,
  async (newId) => {
    if (newId) {
      try {
        const invitee = await inviteesStore.getInvitee(newId)
        if (invitee) {
          currentInvitee.value = { ...invitee }
        }
      } catch (error) {
        console.error('Failed to fetch invitee for editing:', error)
        // Optionally, close the panel or show an error
        emit('saved') // Emit saved to clear the form
      }
    } else {
      currentInvitee.value = { ...defaultInvitee } // Reset for new invitee
    }
  },
  { immediate: true },
)

const saveInvitee = async () => {
  try {
    if (props.inviteeId) {
      await inviteesStore.updateInvitee(props.inviteeId, currentInvitee.value)
    } else {
      await inviteesStore.addInvitee(currentInvitee.value)
    }
    emit('saved')
  } catch (error) {
    console.error('Error saving invitee:', error)
  }
}
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>
