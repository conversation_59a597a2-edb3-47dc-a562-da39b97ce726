import type { Timestamp } from 'firebase/firestore'

export interface UserProfile {
  uid: string
  email: string
  firstName: string
  lastName: string
  displayName: string
  photoURL?: string
  phoneNumber?: string

  // Project tracking (per-project payment model)
  projects: string[] // Array of project IDs user owns or collaborates on

  // Preferences
  preferences: {
    language: string
    timezone: string
    currency: string
    emailNotifications: boolean
    smsNotifications: boolean
    marketingEmails: boolean
  }

  // Metadata
  createdAt: Timestamp
  updatedAt: Timestamp
  lastLoginAt: Timestamp
  emailVerified: boolean
  onboardingCompleted: boolean
}

export interface CreateUserProfileData {
  email: string
  firstName: string
  lastName: string
  uid: string
  photoURL?: string
  phoneNumber?: string
}

// Project-related types
export interface WeddingProject {
  id: string
  name: string
  weddingDate: Timestamp
  venue?: string
  description?: string
  expectedGuests: number

  // Tier information
  tier: string // 'free', 'basic', 'premium', 'gold'
  tierPurchasedAt: Timestamp
  tierExpiresAt?: Timestamp // For data retention

  // Payment information (for paid tiers)
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded' | 'free'
  stripePaymentIntentId?: string
  amountPaid?: number
  currency?: string

  // Usage tracking
  usage: {
    eventsCreated: number
    currentEvents: number
    totalGuests: number
    collaboratorsAdded: number
  }

  // Collaboration
  collaborators: string[] // Array of user UIDs

  // Status
  status: 'planning' | 'active' | 'completed' | 'cancelled'

  // Metadata
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: string // User UID
}

// Event-related types
export interface EventBasic {
  id: string
  projectId: string // Reference to parent project
  name: string
  type: 'ceremony' | 'reception' | 'rehearsal' | 'party' | 'other'
  date: Timestamp
  time?: string
  venue?: string
  description?: string
  status: 'planning' | 'active' | 'completed' | 'cancelled'

  // Guest management
  guestCount: number
  confirmedGuests: number
  pendingGuests: number

  // Metadata
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: string // User UID
}

export interface Guest {
  id: string
  projectId: string // Reference to parent project
  eventIds: string[] // Array of event IDs this guest is invited to

  // Personal information
  firstName: string
  lastName: string
  email?: string
  phoneNumber: string // Primary identifier for duplicates

  // Event-specific details
  status: 'invited' | 'confirmed' | 'declined' | 'maybe' | 'no_response'
  plusOne: boolean
  plusOneName?: string
  dietaryRestrictions?: string
  notes?: string

  // Contact preferences
  preferredContact: 'email' | 'phone' | 'whatsapp'

  // RSVP tracking
  invitedAt?: Timestamp
  respondedAt?: Timestamp

  // Metadata
  createdAt: Timestamp
  updatedAt: Timestamp
  addedBy: string // User UID
}

export interface Collaborator {
  uid: string
  projectId: string
  email: string
  displayName: string
  role: 'owner' | 'admin' | 'editor' | 'viewer'
  permissions: {
    canEditProject: boolean
    canManageGuests: boolean
    canViewAnalytics: boolean
    canManageCollaborators: boolean
    canDeleteProject: boolean
  }
  invitedAt: Timestamp
  acceptedAt?: Timestamp
  status: 'pending' | 'active' | 'inactive'
}
