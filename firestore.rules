rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(ownerId) {
      return isAuthenticated() && request.auth.uid == ownerId;
    }

    // Users collection
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated() && request.auth.uid == userId;
    }

    // Projects collection
    match /projects/{projectId} {
      allow read, write: if isOwner(resource.data.ownerId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.ownerId);
      allow delete: if isOwner(resource.data.ownerId);
    }

    // Events collection
    match /events/{eventId} {
      allow read, write: if isOwner(resource.data.ownerId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.ownerId);
      allow delete: if isOwner(resource.data.ownerId);
    }

    // Guests collection
    match /guests/{guestId} {
      allow read, write: if isOwner(resource.data.ownerId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.ownerId);
      allow delete: if isOwner(resource.data.ownerId);
    }

    // Payments collection
    match /payments/{paymentId} {
      allow read: if isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow write, delete: if false; // Only backend/Stripe webhooks can modify payments
    }
  }
}
