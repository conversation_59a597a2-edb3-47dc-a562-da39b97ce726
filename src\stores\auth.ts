import { defineStore } from 'pinia'
import { auth } from '../firebase'
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  onAuthStateChanged,
  signOut,
  setPersistence,
  browserLocalPersistence,
  updateProfile,
  sendEmailVerification,
  sendPasswordResetEmail,
} from 'firebase/auth'
import type { User as FirebaseUser } from 'firebase/auth'
import { userService } from '@/services/firebase'
import { UserService } from '@/services/userService'
import type { User } from '@/types/firebase'
import type { UserProfile } from '@/types/user'

interface AuthState {
  firebaseUser: FirebaseUser | null
  user: User | null
  userProfile: UserProfile | null
  authIsReady: boolean
  isLoading: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    firebaseUser: null,
    user: null,
    userProfile: null,
    authIsReady: false,
    isLoading: false,
  }),
  getters: {
    isAuthenticated: (state) => !!state.firebaseUser,
    currentUser: (state) => state.user,
    currentUserProfile: (state) => state.userProfile,
    isEmailVerified: (state) => state.firebaseUser?.emailVerified || false,
  },
  actions: {
    async login(email: string, password: string) {
      try {
        this.isLoading = true
        const res = await signInWithEmailAndPassword(auth, email, password)
        this.firebaseUser = res.user

        // Load user profile from Firestore
        if (res.user) {
          this.user = await userService.getUser(res.user.uid)

          // Ensure user profile exists using new service
          this.userProfile = await UserService.ensureUserProfile({
            uid: res.user.uid,
            email: res.user.email!,
            firstName: res.user.displayName?.split(' ')[0] || '',
            lastName: res.user.displayName?.split(' ').slice(1).join(' ') || '',
            phoneNumber: res.user.phoneNumber || undefined,
            photoURL: res.user.photoURL || undefined,
          })
        }
        return null // No error
      } catch (err: unknown) {
        let errorMessage = 'An unknown error occurred.'
        if (err instanceof Error) {
          errorMessage = err.message
        }
        console.error('Login error:', errorMessage)
        return errorMessage // Return error message
      } finally {
        this.isLoading = false
      }
    },

    async loginWithGoogle() {
      try {
        this.isLoading = true
        const provider = new GoogleAuthProvider()
        const res = await signInWithPopup(auth, provider)

        // Check if user document exists, create if not
        let user = await userService.getUser(res.user.uid)
        if (!user) {
          // Create user document for new Google users
          await this.createUserDocument(res.user)
          user = await userService.getUser(res.user.uid)
        }

        // Ensure user profile exists using new service
        this.userProfile = await UserService.ensureUserProfile({
          uid: res.user.uid,
          email: res.user.email!,
          firstName: res.user.displayName?.split(' ')[0] || '',
          lastName: res.user.displayName?.split(' ').slice(1).join(' ') || '',
          phoneNumber: res.user.phoneNumber || undefined,
          photoURL: res.user.photoURL || undefined,
        })

        this.firebaseUser = res.user
        this.user = user
        return null // No error
      } catch (err: unknown) {
        let errorMessage = 'An unknown error occurred.'
        if (err instanceof Error) {
          errorMessage = err.message
        }
        console.error('Google login error:', errorMessage)
        return errorMessage // Return error message
      } finally {
        this.isLoading = false
      }
    },

    async register(
      email: string,
      password: string,
      firstName: string,
      lastName: string,
      sendVerification = true,
    ) {
      try {
        this.isLoading = true
        const res = await createUserWithEmailAndPassword(auth, email, password)

        // Update Firebase Auth profile
        await updateProfile(res.user, {
          displayName: `${firstName} ${lastName}`,
        })

        // Send email verification
        if (sendVerification) {
          await sendEmailVerification(res.user)
        }

        // Create user document in Firestore
        await this.createUserDocument(res.user, firstName, lastName)

        this.firebaseUser = res.user
        this.user = await userService.getUser(res.user.uid)
        return null // No error
      } catch (err: unknown) {
        let errorMessage = 'An unknown error occurred.'
        if (err instanceof Error) {
          errorMessage = err.message
        }
        console.error('Registration error:', errorMessage)
        return errorMessage // Return error message
      } finally {
        this.isLoading = false
      }
    },

    async createUserDocument(firebaseUser: FirebaseUser, firstName?: string, lastName?: string) {
      const displayName = firebaseUser.displayName || `${firstName || ''} ${lastName || ''}`.trim()
      const nameParts = displayName.split(' ')

      // Create user profile using new UserService
      const userProfile = await UserService.ensureUserProfile({
        uid: firebaseUser.uid,
        email: firebaseUser.email!,
        firstName: firstName || nameParts[0] || '',
        lastName: lastName || nameParts.slice(1).join(' ') || '',
        phoneNumber: firebaseUser.phoneNumber || undefined,
        photoURL: firebaseUser.photoURL || undefined,
      })

      this.userProfile = userProfile

      // Also create using old service for backward compatibility
      try {
        await userService.createUser(firebaseUser.uid, {
          email: firebaseUser.email!,
          displayName,
          firstName: firstName || nameParts[0] || '',
          lastName: lastName || nameParts.slice(1).join(' ') || '',
          phoneNumber: firebaseUser.phoneNumber || undefined,
          profilePicture: firebaseUser.photoURL || undefined,
          preferences: {
            language: 'en',
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            theme: 'auto',
          },
          metadata: {
            totalProjects: 0,
            totalGuests: 0,
            freeProjectUsed: false,
          },
        })
      } catch (error) {
        console.warn('Old user service creation failed:', error)
      }
    },

    async sendPasswordReset(email: string) {
      try {
        await sendPasswordResetEmail(auth, email)
        return null // No error
      } catch (err: unknown) {
        let errorMessage = 'An unknown error occurred.'
        if (err instanceof Error) {
          errorMessage = err.message
        }
        console.error('Password reset error:', errorMessage)
        return errorMessage // Return error message
      }
    },

    async resendEmailVerification() {
      try {
        if (this.firebaseUser) {
          await sendEmailVerification(this.firebaseUser)
          return null // No error
        }
        return 'No user logged in'
      } catch (err: unknown) {
        let errorMessage = 'An unknown error occurred.'
        if (err instanceof Error) {
          errorMessage = err.message
        }
        console.error('Email verification error:', errorMessage)
        return errorMessage // Return error message
      }
    },
    async logout() {
      await signOut(auth)
      this.firebaseUser = null
      this.user = null
      this.userProfile = null
    },
    async initAuth(): Promise<void> {
      try {
        // Set browser local persistence first
        await setPersistence(auth, browserLocalPersistence)
      } catch (error) {
        console.error('Error setting persistence:', error)
      }

      return new Promise((resolve) => {
        let hasResolved = false

        // Set up persistent auth state listener
        onAuthStateChanged(auth, async (firebaseUser) => {
          this.firebaseUser = firebaseUser

          if (firebaseUser) {
            // Load user profile from Firestore
            this.user = await userService.getUser(firebaseUser.uid)
          } else {
            this.user = null
          }

          if (!hasResolved) {
            this.authIsReady = true
            hasResolved = true
            resolve()
          }
        })
      })
    },
  },
  getters: {
    isAuthenticated: (state) => !!state.firebaseUser,
    isEmailVerified: (state) => state.firebaseUser?.emailVerified ?? false,
    userDisplayName: (state) =>
      state.user?.displayName || state.firebaseUser?.displayName || 'User',
    userEmail: (state) => state.user?.email || state.firebaseUser?.email || '',
  },
})
