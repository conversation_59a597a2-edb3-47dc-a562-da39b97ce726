# Wedding Guest Management App - Commercial Development Plan

## Overview

This document outlines the development plan for a commercial wedding guest management platform using Vue 3, TypeScript, Tailwind CSS, PrimeVue, and Firebase. The platform focuses on manual guest management with projections and analytics.

## Backend

The current firebase config is for a different database, don't make any changes to it. I will create a new firebase project and update the firebase config in `src/firebase.ts` with the new project's config.

## Core Features

### 1. Authentication & User Management

- [x] Basic authentication (existing)
- [ ] User registration flow
- [ ] Email verification
- [ ] Password reset functionality
- [ ] User profile management

### 2. Project & Payment System

- [ ] One-time payment per project creation
- [ ] Project bundles with different event limits:
  - Basic Bundle: 3 events per project
  - Premium Bundle: 6 events per project
  - Ultimate Bundle: 12 events per project
- [ ] Free version with severe limitations (1 project, 1 event, 50 guests max)
- [ ] Payment processing (Stripe integration)
- [ ] Project selection UI

### 3. Project & Event Management

- [ ] Create project-based structure (replaces multi-event per user)
- [ ] Multiple events per project with custom names
- [ ] Set event dates and details
- [ ] Project expiration based on final event date
- [ ] Data export functionality (Excel/CSV)
- [ ] Duplicate prevention by phone number

### 4. Guest Management (Manual Entry Focus)

- [x] Basic invitee CRUD (existing)
- [ ] Enhanced guest details (name, phone number)
- [ ] Bulk guest operations
- [ ] Expected guest count per invitee per event (manual entry)
- [ ] Duplicate detection by phone number
- [ ] Integration with companion mobile app for contact import

### 5. Analytics & Projections

- [ ] Dashboard with key metrics per event
- [ ] Total expected guests per event
- [ ] Guest projections when not all guests are filled
- [ ] Visual charts and graphs
- [ ] Export reports

## Future Features (Post-MVP)

### RSVP System (Phase 2)

- [ ] Guest RSVP functionality
- [ ] Flexible guest count (not limited to +1)
- [ ] RSVP limits set by event owner
- [ ] RSVP tracking and management

## UI Layout & Implementation Plan

### Phase 1: Core Infrastructure

#### Task 1.1: Setup Authentication System

- [ ] Enhance Firebase auth configuration
- [ ] Create registration page with PrimeVue components
- [ ] Implement email verification
- [ ] Build user profile page

#### Task 1.2: Create Project & Payment System

- [ ] Design project bundles database structure
- [ ] Implement Stripe integration for one-time payments
- [ ] Create bundle selection UI with PrimeVue Cards
- [ ] Build project management page
- [ ] Implement free version limitations

### Phase 2: Project & Event Management

#### Task 2.1: Project-Based Structure

- [ ] Design database schema for project-based multi-event support
- [ ] Create project management UI
- [ ] Implement project creation with bundle selection
- [ ] Build project dashboard

#### Task 2.2: Event Management

- [ ] Create event management UI within projects
- [ ] Implement event creation/editing with PrimeVue Calendar and Forms
- [ ] Build event settings page
- [ ] Add custom event naming

### Phase 3: Enhanced Guest Management

#### Task 3.1: Guest System Upgrade

- [ ] Enhance guest data model (focus on name + phone)
- [ ] Rebuild guest management UI with PrimeVue DataTable
- [ ] Implement duplicate detection by phone number
- [ ] Create detailed guest view

#### Task 3.2: Manual Guest Tracking

- [ ] Implement expected guest count per invitee per event
- [ ] Create bulk operations for guest management
- [ ] Build guest list exports
- [ ] Add companion app integration planning

### Phase 4: Analytics & Projections

#### Task 4.1: Event Analytics Dashboard

- [ ] Design analytics dashboard layout per event
- [ ] Implement total expected guests calculations
- [ ] Create projection algorithms for incomplete data
- [ ] Build visualization components with PrimeVue Charts

#### Task 4.2: Reporting & Exports

- [ ] Create exportable reports per event
- [ ] Implement guest projection visualizations
- [ ] Build Excel/CSV export functionality

## UI Components Needed

### Layout Components

- PrimeVue Menu/Menubar - For navigation
- PrimeVue Panel/Card - For content sections
- PrimeVue TabView - For organizing related content
- PrimeVue Splitter - For resizable sections

### Data Components

- PrimeVue DataTable - For invitee management
- PrimeVue Chart - For analytics and projections
- PrimeVue Calendar - For event scheduling
- PrimeVue FileUpload - For importing invitee data

### Form Components

- PrimeVue InputText/InputNumber - For data entry
- PrimeVue Dropdown/MultiSelect - For selections
- PrimeVue Button - For actions
- PrimeVue Dialog - For confirmations and modals

### Notification Components

- PrimeVue Toast - For system notifications
- PrimeVue Message - For inline messages
- PrimeVue ProgressBar - For loading indicators

## Database Structure

### Collections

- users
- projects
- events (within projects)
- guests (replaces invitees)
- payments (one-time project purchases)

## Next Steps

1. Begin with authentication enhancements
2. Implement project & payment system
3. Build project-based event structure
4. Enhance guest management with duplicate detection
5. Develop analytics and projections dashboard
6. Plan companion mobile app integration
