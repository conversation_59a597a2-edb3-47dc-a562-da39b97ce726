import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

export function useLocale() {
  const { locale, t } = useI18n()

  // RTL languages
  const rtlLanguages = ['ar', 'he']

  // Computed property to check if current locale is RTL
  const isRTL = computed(() => rtlLanguages.includes(locale.value))

  // Computed property for direction
  const direction = computed(() => isRTL.value ? 'rtl' : 'ltr')

  // Function to change locale
  const changeLocale = (newLocale: string) => {
    locale.value = newLocale
    // Store in localStorage for persistence
    localStorage.setItem('locale', newLocale)
  }

  // Function to get available locales
  const availableLocales = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ar', name: 'العربية', flag: '🇸🇦' },
    { code: 'he', name: 'עברית', flag: '🇮🇱' }
  ]

  // Watch for locale changes and update document direction
  watch(
    () => locale.value,
    (newLocale) => {
      // Update document direction
      document.documentElement.dir = rtlLanguages.includes(newLocale) ? 'rtl' : 'ltr'
      // Update document lang attribute
      document.documentElement.lang = newLocale
      // Update body class for RTL styling
      if (rtlLanguages.includes(newLocale)) {
        document.body.classList.add('rtl')
        document.body.classList.remove('ltr')
      } else {
        document.body.classList.add('ltr')
        document.body.classList.remove('rtl')
      }
    },
    { immediate: true }
  )

  // Initialize locale from localStorage on first load
  const initializeLocale = () => {
    const savedLocale = localStorage.getItem('locale')
    if (savedLocale && availableLocales.some(l => l.code === savedLocale)) {
      locale.value = savedLocale
    }
  }

  return {
    locale,
    isRTL,
    direction,
    changeLocale,
    availableLocales,
    initializeLocale,
    t
  }
}
