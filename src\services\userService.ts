import { doc, getDoc, setDoc, updateDoc, serverTimestamp, type Timestamp } from 'firebase/firestore'
import { db } from '@/firebase'
import type { UserProfile, CreateUserProfileData } from '@/types/user'

export class UserService {
  /**
   * Create a new user profile document
   */
  static async createUserProfile(userData: CreateUserProfileData): Promise<UserProfile> {
    const userRef = doc(db, 'users', userData.uid)

    // Parse name from displayName if firstName/lastName not provided
    let firstName = userData.firstName
    let lastName = userData.lastName

    if (!firstName || !lastName) {
      const nameParts = userData.email.split('@')[0].split('.')
      firstName = firstName || nameParts[0] || 'User'
      lastName = lastName || nameParts[1] || ''
    }

    const userProfile: Omit<UserProfile, 'createdAt' | 'updatedAt' | 'lastLoginAt'> = {
      uid: userData.uid,
      email: userData.email,
      firstName: firstName,
      lastName: lastName,
      displayName: `${firstName} ${lastName}`.trim(),
      photoURL: userData.photoURL,
      phoneNumber: userData.phoneNumber,
      projects: [],
      preferences: {
        language: 'en',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC',
        currency: 'USD',
        emailNotifications: true,
        smsNotifications: false,
        marketingEmails: true,
      },
      emailVerified: false,
      onboardingCompleted: false,
    }

    const profileWithTimestamps = {
      ...userProfile,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLoginAt: serverTimestamp(),
    }

    await setDoc(userRef, profileWithTimestamps)

    // Return the profile with current timestamp (approximation)
    const now = new Date()
    return {
      ...userProfile,
      createdAt: { seconds: Math.floor(now.getTime() / 1000), nanoseconds: 0 } as Timestamp,
      updatedAt: { seconds: Math.floor(now.getTime() / 1000), nanoseconds: 0 } as Timestamp,
      lastLoginAt: { seconds: Math.floor(now.getTime() / 1000), nanoseconds: 0 } as Timestamp,
    }
  }

  /**
   * Get user profile by UID
   */
  static async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userRef = doc(db, 'users', uid)
      const userSnap = await getDoc(userRef)

      if (userSnap.exists()) {
        return userSnap.data() as UserProfile
      }

      return null
    } catch (error) {
      console.error('Error getting user profile:', error)
      return null
    }
  }

  /**
   * Update user profile
   */
  static async updateUserProfile(uid: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      })
    } catch (error) {
      console.error('Error updating user profile:', error)
      throw error
    }
  }

  /**
   * Update last login timestamp
   */
  static async updateLastLogin(uid: string): Promise<void> {
    try {
      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        lastLoginAt: serverTimestamp(),
      })
    } catch (error) {
      console.error('Error updating last login:', error)
      // Don't throw error for login timestamp updates
    }
  }

  /**
   * Check if user profile exists, create if it doesn't
   */
  static async ensureUserProfile(userData: CreateUserProfileData): Promise<UserProfile> {
    try {
      // First, try to get existing profile
      let profile = await this.getUserProfile(userData.uid)

      if (!profile) {
        // Create new profile if it doesn't exist
        console.log('Creating new user profile for:', userData.email)
        profile = await this.createUserProfile(userData)
      } else {
        // Update last login for existing users
        await this.updateLastLogin(userData.uid)

        // Update email verification status if it changed
        if (profile.emailVerified !== (userData as any).emailVerified) {
          await this.updateUserProfile(userData.uid, {
            emailVerified: (userData as any).emailVerified || false,
          })
          profile.emailVerified = (userData as any).emailVerified || false
        }
      }

      return profile
    } catch (error) {
      console.error('Error ensuring user profile:', error)
      throw error
    }
  }

  /**
   * Add project to user's project list
   */
  static async addProjectToUser(uid: string, projectId: string): Promise<void> {
    try {
      const profile = await this.getUserProfile(uid)
      if (!profile) {
        throw new Error('User profile not found')
      }

      if (!profile.projects.includes(projectId)) {
        const updatedProjects = [...profile.projects, projectId]
        await this.updateUserProfile(uid, { projects: updatedProjects })
      }
    } catch (error) {
      console.error('Error adding project to user:', error)
      throw error
    }
  }

  /**
   * Remove project from user's project list
   */
  static async removeProjectFromUser(uid: string, projectId: string): Promise<void> {
    try {
      const profile = await this.getUserProfile(uid)
      if (!profile) {
        throw new Error('User profile not found')
      }

      const updatedProjects = profile.projects.filter((id) => id !== projectId)
      await this.updateUserProfile(uid, { projects: updatedProjects })
    } catch (error) {
      console.error('Error removing project from user:', error)
      throw error
    }
  }

  /**
   * Mark onboarding as completed
   */
  static async completeOnboarding(uid: string): Promise<void> {
    try {
      await this.updateUserProfile(uid, { onboardingCompleted: true })
    } catch (error) {
      console.error('Error completing onboarding:', error)
      throw error
    }
  }
}
