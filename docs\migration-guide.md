# Migration Guide: From Single-Event to Multi-Event Architecture

## Overview

This guide outlines the steps to migrate your existing single-event wedding guest management system to a multi-tenant, multi-event SaaS architecture.

## Current State Analysis

### Existing Structure
- **Collection**: `invitees`
- **Structure**: Single collection with hardcoded event types (`houseParty`, `henna`, `wedding`)
- **User Association**: `invitedByUid` field links invitees to users
- **Authentication**: Firebase Auth with basic user management

### Target Structure
- **Collections**: `users`, `events`, `invitees`, `collaborators`, `subscriptions`, `activities`
- **Multi-Event Support**: Each user can create multiple events
- **Flexible Event Types**: Configurable event types and settings
- **Collaboration**: Multiple users can collaborate on events
- **Subscription Management**: Tiered access control

## Migration Steps

### Phase 1: Backup and Preparation

1. **Backup Current Data**
   ```bash
   # Export existing data
   gcloud firestore export gs://your-backup-bucket/backup-$(date +%Y%m%d)
   ```

2. **Create New Firebase Project** (Recommended)
   - Set up new Firebase project for production
   - Keep existing project for development/testing
   - Update `src/firebase.ts` with new project config

3. **Deploy New Security Rules**
   - Upload the new `firestore.rules` to your Firebase project
   - Test rules in Firebase Console simulator

### Phase 2: Data Structure Migration

#### Step 1: Create Default Events for Existing Users

```typescript
// Migration script to run once
import { collection, getDocs, doc, setDoc, serverTimestamp } from 'firebase/firestore'
import { db } from './firebase'

async function createDefaultEventsForUsers() {
  // Get all unique users from existing invitees
  const inviteesSnapshot = await getDocs(collection(db, 'invitees'))
  const userIds = new Set<string>()
  
  inviteesSnapshot.forEach(doc => {
    const data = doc.data()
    if (data.invitedByUid) {
      userIds.add(data.invitedByUid)
    }
  })

  // Create default events for each user
  for (const userId of userIds) {
    const eventTypes = ['houseParty', 'henna', 'wedding']
    
    for (const eventType of eventTypes) {
      const eventId = `${userId}_${eventType}`
      await setDoc(doc(db, 'events', eventId), {
        ownerId: userId,
        name: eventType === 'houseParty' ? 'House Party' : 
              eventType === 'henna' ? 'Henna Ceremony' : 'Wedding',
        type: eventType === 'wedding' ? 'wedding' : 'other',
        date: serverTimestamp(), // You'll need to set proper dates
        venue: {
          name: 'TBD',
          address: '',
          city: '',
          country: ''
        },
        settings: {
          allowGuestPlusOnes: false,
          requirePhoneNumber: false,
          requireAddress: false,
          customFields: [],
          invitationTemplate: 'default'
        },
        status: 'draft',
        privacy: 'private',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        metadata: {
          totalInvitees: 0,
          confirmedGuests: 0,
          pendingGuests: 0,
          declinedGuests: 0,
          lastActivity: serverTimestamp()
        }
      })
    }
  }
}
```

#### Step 2: Migrate Existing Invitees

```typescript
async function migrateInvitees() {
  const inviteesSnapshot = await getDocs(collection(db, 'invitees'))
  const batch = writeBatch(db)
  
  inviteesSnapshot.forEach(doc => {
    const oldData = doc.data()
    
    // Create separate invitee records for each event they're invited to
    const events = oldData.events || {}
    
    Object.keys(events).forEach(eventType => {
      if (events[eventType]) {
        const eventId = `${oldData.invitedByUid}_${eventType}`
        const newInviteeId = `${doc.id}_${eventType}`
        
        const newInviteeData = {
          eventId,
          ownerId: oldData.invitedByUid,
          firstName: oldData.firstName || '',
          lastName: oldData.lastName || '',
          phoneNumber: oldData.phoneNumber || '',
          email: '', // Add if available
          rsvpStatus: 'pending',
          guestCount: 0,
          maxGuests: oldData.confirmedGuests || 1,
          plusOnes: [],
          invitedBy: oldData.invitedByUid,
          invitedAt: oldData.createdAt || serverTimestamp(),
          tags: [],
          metadata: {
            invitationsSent: 0,
            communicationHistory: []
          },
          createdAt: oldData.createdAt || serverTimestamp(),
          updatedAt: serverTimestamp()
        }
        
        batch.set(doc(db, 'new_invitees', newInviteeId), newInviteeData)
      }
    })
  })
  
  await batch.commit()
}
```

#### Step 3: Create User Documents

```typescript
async function createUserDocuments() {
  // Get unique users from Firebase Auth
  // This would typically be done via Admin SDK
  
  const userIds = ['user1', 'user2'] // Replace with actual user IDs
  
  for (const userId of userIds) {
    await setDoc(doc(db, 'users', userId), {
      email: '<EMAIL>', // Get from Firebase Auth
      displayName: 'User Name',
      firstName: 'User',
      lastName: 'Name',
      subscriptionStatus: 'trial',
      subscriptionPlan: 'free',
      preferences: {
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          rsvpUpdates: true,
          eventReminders: true
        },
        theme: 'auto'
      },
      metadata: {
        totalEvents: 0,
        totalGuests: 0,
        storageUsed: 0
      },
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      lastLoginAt: serverTimestamp()
    })
  }
}
```

### Phase 3: Application Code Migration

#### Step 1: Update Stores
- Replace `src/stores/invitees.ts` with new event-based structure
- Update authentication store to handle user documents
- Add new events store for event management

#### Step 2: Update Components
- Modify existing components to work with event-scoped data
- Add event selection/switching functionality
- Update forms to include event context

#### Step 3: Update Routes
- Add event-specific routes (`/events/:eventId/guests`)
- Update navigation to include event context
- Add event management pages

### Phase 4: Testing and Validation

1. **Data Integrity Checks**
   - Verify all users have corresponding user documents
   - Ensure all invitees are properly linked to events
   - Check that event metadata is accurate

2. **Functionality Testing**
   - Test user registration and login
   - Verify event creation and management
   - Test guest management within events
   - Validate real-time updates

3. **Performance Testing**
   - Test with realistic data volumes
   - Verify query performance with indexes
   - Check real-time listener efficiency

### Phase 5: Cleanup

1. **Remove Old Collections**
   ```typescript
   // After verifying migration success
   // Delete old 'invitees' collection
   // This should be done carefully with proper backups
   ```

2. **Update Security Rules**
   - Remove any legacy rule references
   - Ensure all new collections are properly secured

3. **Update Documentation**
   - Update API documentation
   - Update user guides
   - Document new features

## Rollback Plan

1. **Keep Old Data**: Don't delete original collections until migration is fully verified
2. **Feature Flags**: Use feature flags to switch between old and new systems
3. **Gradual Migration**: Migrate users in batches rather than all at once
4. **Monitoring**: Set up monitoring to detect issues early

## Post-Migration Benefits

- ✅ Multi-event support per user
- ✅ Scalable architecture for SaaS growth
- ✅ Proper data isolation and security
- ✅ Collaboration features
- ✅ Subscription management ready
- ✅ Analytics and reporting capabilities
- ✅ Better performance with proper indexing

## Timeline Estimate

- **Phase 1**: 1-2 days
- **Phase 2**: 3-5 days
- **Phase 3**: 5-7 days
- **Phase 4**: 2-3 days
- **Phase 5**: 1-2 days

**Total**: 2-3 weeks for complete migration
