<template>
  <div
    class="min-h-screen flex items-center justify-center bg-surface-50 dark:bg-surface-900 py-12 px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full">
      <!-- Logo/Brand Section -->
      <div class="text-center mb-8">
        <div
          class="mx-auto h-16 w-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-4 shadow-lg"
        >
          <i class="pi pi-heart-fill text-white text-2xl"></i>
        </div>
        <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">Invity</h1>
        <h2 class="text-xl font-semibold text-surface-700 dark:text-surface-300">
          {{ $t('auth.forgotPassword') }}
        </h2>
        <p class="mt-2 text-sm text-surface-600 dark:text-surface-400">
          {{ $t('auth.forgotPasswordDescription') }}
        </p>
      </div>

      <!-- Auth Card -->
      <Card
        class="shadow-xl border border-surface-200 dark:border-surface-700 overflow-hidden bg-white dark:bg-surface-800"
      >
        <template #content>
          <form v-if="!emailSent" @submit.prevent="handleSubmit" class="space-y-6">
            <div>
              <label
                for="email"
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                {{ $t('auth.email') }}
              </label>
              <InputGroup>
                <InputGroupAddon class="bg-surface-100 dark:bg-surface-700">
                  <i class="pi pi-envelope text-surface-600 dark:text-surface-300"></i>
                </InputGroupAddon>
                <InputText
                  id="email"
                  v-model="email"
                  type="email"
                  :placeholder="$t('auth.emailPlaceholder')"
                  :invalid="!!emailError"
                  class="w-full"
                  required
                />
              </InputGroup>
              <small v-if="emailError" class="text-red-500 mt-1 block">{{ emailError }}</small>
            </div>

            <!-- Error Message -->
            <Message v-if="errorMessage" severity="error" :closable="false" class="mb-4">
              {{ errorMessage }}
            </Message>

            <!-- Submit Button -->
            <Button
              type="submit"
              :loading="isLoading"
              class="w-full"
              size="large"
              :disabled="isLoading"
            >
              <i class="pi pi-send mr-2"></i>
              {{ $t('auth.sendResetEmail') }}
            </Button>

            <!-- Back to Login -->
            <div class="text-center mt-4">
              <router-link
                to="/login"
                class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 transition-colors"
              >
                <i class="pi pi-arrow-left mr-1"></i>
                {{ $t('auth.backToLogin') }}
              </router-link>
            </div>
          </form>

          <!-- Success Message -->
          <div v-else class="text-center space-y-6">
            <div
              class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900 shadow-lg"
            >
              <i class="pi pi-check text-green-600 dark:text-green-400 text-2xl"></i>
            </div>

            <div>
              <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
                {{ $t('auth.resetEmailSent') }}
              </h3>
              <p class="mt-2 text-sm text-surface-600 dark:text-surface-400">
                {{ $t('auth.resetEmailSentDescription', { email }) }}
              </p>
            </div>

            <div class="space-y-4">
              <Button
                @click="resendEmail"
                :loading="isLoading"
                severity="secondary"
                outlined
                class="w-full"
                size="large"
                :disabled="isLoading"
              >
                <i class="pi pi-refresh mr-2"></i>
                {{ $t('auth.resendEmail') }}
              </Button>

              <div class="text-center">
                <router-link
                  to="/login"
                  class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 transition-colors"
                >
                  <i class="pi pi-arrow-left mr-1"></i>
                  {{ $t('auth.backToLogin') }}
                </router-link>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useI18n } from 'vue-i18n'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import InputGroup from 'primevue/inputgroup'
import InputGroupAddon from 'primevue/inputgroupaddon'
import Button from 'primevue/button'
import Message from 'primevue/message'

const authStore = useAuthStore()
const { t } = useI18n()

const email = ref('')
const emailError = ref('')
const errorMessage = ref('')
const isLoading = ref(false)
const emailSent = ref(false)

const validateEmail = () => {
  emailError.value = ''

  if (!email.value.trim()) {
    emailError.value = t('auth.errors.emailRequired')
    return false
  } else if (!/\S+@\S+\.\S+/.test(email.value)) {
    emailError.value = t('auth.errors.emailInvalid')
    return false
  }

  return true
}

const handleSubmit = async () => {
  if (!validateEmail()) return

  errorMessage.value = ''
  isLoading.value = true

  const error = await authStore.sendPasswordReset(email.value)

  if (error) {
    errorMessage.value = error
  } else {
    emailSent.value = true
  }

  isLoading.value = false
}

const resendEmail = async () => {
  errorMessage.value = ''
  isLoading.value = true

  const error = await authStore.sendPasswordReset(email.value)

  if (error) {
    errorMessage.value = error
    emailSent.value = false
  }

  isLoading.value = false
}
</script>
