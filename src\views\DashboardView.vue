<template>
  <AppLayout>
    <!-- Dashboard Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('dashboard.title') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            {{ t('dashboard.welcome') }}
          </p>
        </div>
        <div class="flex space-x-3 rtl:space-x-reverse">
          <Button :label="t('dashboard.createProject')" icon="pi pi-plus" @click="createProject" />
          <Button
            :label="t('dashboard.addGuest')"
            icon="pi pi-user-plus"
            outlined
            @click="addGuest"
          />
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ stats.totalEvents }}</div>
              <div class="text-blue-100 text-sm">{{ t('dashboard.totalEvents') }}</div>
            </div>
            <i class="pi pi-calendar text-3xl text-blue-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-green-500 to-green-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ stats.totalGuests }}</div>
              <div class="text-green-100 text-sm">{{ t('dashboard.totalGuests') }}</div>
            </div>
            <i class="pi pi-users text-3xl text-green-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ stats.confirmedGuests }}</div>
              <div class="text-purple-100 text-sm">{{ t('events.confirmedGuests') }}</div>
            </div>
            <i class="pi pi-check-circle text-3xl text-purple-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">{{ stats.pendingGuests }}</div>
              <div class="text-orange-100 text-sm">{{ t('events.pendingGuests') }}</div>
            </div>
            <i class="pi pi-clock text-3xl text-orange-200"></i>
          </div>
        </template>
      </Card>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Upcoming Events -->
      <div class="lg:col-span-2">
        <Card>
          <template #title>
            <div class="flex items-center justify-between">
              <span>{{ t('dashboard.upcomingEvents') }}</span>
              <Button :label="t('common.viewAll')" text size="small" @click="viewAllEvents" />
            </div>
          </template>
          <template #content>
            <div v-if="upcomingEvents.length === 0" class="text-center py-8">
              <i class="pi pi-calendar text-4xl text-surface-400 mb-4"></i>
              <p class="text-surface-600 dark:text-surface-400">
                {{ t('events.noEvents') }}
              </p>
              <Button :label="t('events.createEvent')" class="mt-4" @click="createEvent" />
            </div>
            <div v-else class="space-y-4">
              <div
                v-for="event in upcomingEvents"
                :key="event.id"
                class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg hover:bg-surface-50 dark:hover:bg-surface-800 transition-colors"
              >
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                  <div
                    class="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center"
                  >
                    <i class="pi pi-calendar text-primary-600 dark:text-primary-400"></i>
                  </div>
                  <div>
                    <h4 class="font-semibold text-surface-900 dark:text-surface-0">
                      {{ event.name }}
                    </h4>
                    <p class="text-sm text-surface-600 dark:text-surface-400">
                      {{ formatDate(event.date) }} • {{ event.guestCount }}
                      {{ t('guests.title').toLowerCase() }}
                    </p>
                  </div>
                </div>
                <div class="flex space-x-2 rtl:space-x-reverse">
                  <Button icon="pi pi-eye" text size="small" @click="viewEvent(event.id)" />
                  <Button icon="pi pi-pencil" text size="small" @click="editEvent(event.id)" />
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>

      <!-- Quick Actions & Recent Activity -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <Card>
          <template #title>{{ t('dashboard.quickActions') }}</template>
          <template #content>
            <div class="space-y-3">
              <Button
                :label="t('dashboard.createEvent')"
                icon="pi pi-plus"
                class="w-full"
                @click="createEvent"
              />
              <Button
                :label="t('dashboard.addGuest')"
                icon="pi pi-user-plus"
                class="w-full"
                outlined
                @click="addGuest"
              />
              <Button
                :label="t('dashboard.viewReports')"
                icon="pi pi-chart-bar"
                class="w-full"
                outlined
                @click="viewReports"
              />
              <Button
                :label="t('guests.importGuests')"
                icon="pi pi-upload"
                class="w-full"
                outlined
                @click="importGuests"
              />
            </div>
          </template>
        </Card>

        <!-- Recent Activity -->
        <Card>
          <template #title>{{ t('dashboard.recentActivity') }}</template>
          <template #content>
            <div v-if="recentActivity.length === 0" class="text-center py-4">
              <p class="text-surface-600 dark:text-surface-400 text-sm">No recent activity</p>
            </div>
            <div v-else class="space-y-3">
              <div
                v-for="activity in recentActivity"
                :key="activity.id"
                class="flex items-start space-x-3 rtl:space-x-reverse"
              >
                <div
                  class="w-8 h-8 bg-surface-100 dark:bg-surface-700 rounded-full flex items-center justify-center flex-shrink-0"
                >
                  <i :class="[activity.icon, 'text-xs text-surface-600 dark:text-surface-400']"></i>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-surface-900 dark:text-surface-0">
                    {{ activity.description }}
                  </p>
                  <p class="text-xs text-surface-500 dark:text-surface-400">
                    {{ formatRelativeTime(activity.timestamp) }}
                  </p>
                </div>
              </div>
            </div>
          </template>
        </Card>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'

const router = useRouter()
const { t } = useLocale()

// Mock data - replace with real data from stores
const stats = ref({
  totalEvents: 5,
  totalGuests: 247,
  confirmedGuests: 189,
  pendingGuests: 58,
})

const upcomingEvents = ref([
  {
    id: '1',
    name: 'Wedding Ceremony',
    date: new Date('2024-08-15'),
    guestCount: 150,
  },
  {
    id: '2',
    name: 'Henna Night',
    date: new Date('2024-08-13'),
    guestCount: 80,
  },
  {
    id: '3',
    name: 'Reception Party',
    date: new Date('2024-08-16'),
    guestCount: 200,
  },
])

const recentActivity = ref([
  {
    id: '1',
    description: 'John Doe confirmed attendance',
    icon: 'pi pi-check',
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
  },
  {
    id: '2',
    description: 'New guest added: Jane Smith',
    icon: 'pi pi-user-plus',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
  },
  {
    id: '3',
    description: 'Wedding Ceremony event updated',
    icon: 'pi pi-pencil',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
  },
])

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date)
}

const formatRelativeTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}m ago`
  } else if (hours < 24) {
    return `${hours}h ago`
  } else {
    return `${days}d ago`
  }
}

const createProject = () => {
  router.push('/projects/create')
}

const createEvent = () => {
  router.push('/events/create')
}

const addGuest = () => {
  router.push('/guests/add')
}

const viewAllEvents = () => {
  router.push('/events')
}

const viewEvent = (id: string) => {
  router.push(`/events/${id}`)
}

const editEvent = (id: string) => {
  router.push(`/events/${id}/edit`)
}

const viewReports = () => {
  router.push('/analytics')
}

const importGuests = () => {
  router.push('/guests/import')
}

onMounted(() => {
  // Load dashboard data
  console.log('Dashboard mounted')
})
</script>
