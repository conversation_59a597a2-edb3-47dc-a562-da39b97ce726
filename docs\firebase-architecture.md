# Firebase Architecture for Project-Based Wedding Guest Management

## Database Structure

### Core Collections

#### 1. `users` Collection

```
users/{userId}
├── email: string
├── displayName: string
├── firstName: string
├── lastName: string
├── phoneNumber?: string
├── profilePicture?: string
├── createdAt: timestamp
├── updatedAt: timestamp
├── lastLoginAt: timestamp
├── preferences: {
│   ├── language: 'en' | 'ar' | 'he'
│   ├── timezone: string
│   └── theme: 'light' | 'dark' | 'auto'
│   }
└── metadata: {
    ├── totalProjects: number
    ├── totalGuests: number
    └── freeProjectUsed: boolean
    }
```

#### 2. `projects` Collection

```
projects/{projectId}
├── ownerId: string (userId)
├── name: string
├── description?: string
├── bundle: 'free' | 'basic' | 'premium' | 'ultimate'
├── bundleLimits: {
│   ├── maxEvents: number (1 for free, 3 for basic, 6 for premium, 12 for ultimate)
│   ├── maxGuestsPerEvent: number (50 for free, unlimited for paid)
│   }
├── paymentId?: string (reference to payment record)
├── status: 'active' | 'expired'
├── expiresAt?: timestamp (based on final event date + buffer)
├── createdAt: timestamp
├── updatedAt: timestamp
└── metadata: {
    ├── totalEvents: number
    ├── totalGuests: number
    └── lastActivity: timestamp
    }
```

#### 3. `events` Collection

```
events/{eventId}
├── projectId: string
├── ownerId: string (for quick access control)
├── name: string (custom name set by user)
├── description?: string
├── date: timestamp
├── venue?: {
│   ├── name: string
│   ├── address: string
│   └── city: string
│   }
├── status: 'active' | 'completed'
├── createdAt: timestamp
├── updatedAt: timestamp
└── metadata: {
    ├── totalGuests: number
    ├── expectedGuests: number (sum of all guest expectations)
    ├── guestsWithExpectations: number (how many guests have expectations set)
    └── lastActivity: timestamp
    }
```

#### 4. `guests` Collection

```
guests/{guestId}
├── projectId: string
├── ownerId: string (for security/access control)
├── firstName: string
├── lastName: string
├── phoneNumber: string (required, used for duplicate detection)
├── notes?: string
├── expectedGuests: { [eventId: string]: number } (expected count per event)
├── addedBy: string (user who added them)
├── addedAt: timestamp
├── updatedAt: timestamp
└── metadata: {
    ├── invitationsSent: number
    ├── lastInvitationSent?: timestamp
    └── communicationHistory: Array<{
        ├── type: 'invitation' | 'reminder' | 'update'
        ├── method: 'email' | 'sms' | 'whatsapp'
        ├── sentAt: timestamp
        └── status: 'sent' | 'delivered' | 'opened' | 'failed'
        }>
    }
```

#### 4. `collaborators` Collection

```
collaborators/{collaboratorId}
├── eventId: string
├── userId: string (the collaborator's user ID)
├── invitedBy: string (user ID who invited them)
├── email: string
├── role: 'viewer' | 'editor' | 'admin'
├── permissions: {
│   ├── canViewGuests: boolean
│   ├── canEditGuests: boolean
│   ├── canDeleteGuests: boolean
│   ├── canInviteGuests: boolean
│   ├── canViewAnalytics: boolean
│   ├── canEditEvent: boolean
│   └── canManageCollaborators: boolean
│   }
├── status: 'pending' | 'accepted' | 'declined'
├── invitedAt: timestamp
├── acceptedAt?: timestamp
├── lastActiveAt?: timestamp
└── invitationToken?: string (for pending invitations)
```

#### 5. `subscriptions` Collection

```
subscriptions/{subscriptionId}
├── userId: string
├── plan: 'free' | 'basic' | 'pro' | 'enterprise'
├── status: 'trial' | 'active' | 'cancelled' | 'expired' | 'past_due'
├── stripeCustomerId?: string
├── stripeSubscriptionId?: string
├── currentPeriodStart: timestamp
├── currentPeriodEnd: timestamp
├── trialEnd?: timestamp
├── cancelAtPeriodEnd: boolean
├── features: {
│   ├── maxEvents: number
│   ├── maxGuestsPerEvent: number
│   ├── maxCollaborators: number
│   ├── hasAnalytics: boolean
│   ├── hasCustomBranding: boolean
│   ├── hasAdvancedReports: boolean
│   └── storageLimit: number (in MB)
│   }
├── usage: {
│   ├── eventsUsed: number
│   ├── guestsUsed: number
│   ├── collaboratorsUsed: number
│   └── storageUsed: number
│   }
├── createdAt: timestamp
└── updatedAt: timestamp
```

#### 6. `activities` Collection (for audit trail)

```
activities/{activityId}
├── eventId?: string
├── userId: string
├── type: 'event_created' | 'guest_added' | 'guest_updated' | 'rsvp_received' | 'collaborator_invited' | etc.
├── description: string
├── metadata: { [key: string]: any }
├── ipAddress?: string
├── userAgent?: string
└── createdAt: timestamp
```

### Security Rules Structure

#### Key Security Principles:

1. **User Isolation**: Users can only access their own data
2. **Event-based Access**: Collaborators can only access events they're invited to
3. **Role-based Permissions**: Different access levels for different roles
4. **Subscription Limits**: Enforce plan limits at the database level

#### Sample Security Rules:

```javascript
// Users can only read/write their own user document
match /users/{userId} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}

// Events can be read/written by owner or collaborators
match /events/{eventId} {
  allow read, write: if request.auth != null &&
    (resource.data.ownerId == request.auth.uid ||
     exists(/databases/$(database)/documents/collaborators/$(request.auth.uid + '_' + eventId)));
}

// Invitees can be accessed by event owner or collaborators with permission
match /invitees/{inviteeId} {
  allow read: if request.auth != null &&
    (resource.data.ownerId == request.auth.uid ||
     hasCollaboratorAccess(resource.data.eventId, 'canViewGuests'));
  allow write: if request.auth != null &&
    (resource.data.ownerId == request.auth.uid ||
     hasCollaboratorAccess(resource.data.eventId, 'canEditGuests'));
}
```

### Data Access Patterns

#### 1. **User Dashboard Queries**

- Get user's events: `events` where `ownerId == currentUser.uid`
- Get collaborated events: `collaborators` where `userId == currentUser.uid` and `status == 'accepted'`

#### 2. **Event Management Queries**

- Get event guests: `invitees` where `eventId == selectedEvent.id`
- Get event collaborators: `collaborators` where `eventId == selectedEvent.id`

#### 3. **Analytics Queries**

- RSVP statistics: Aggregate `invitees` by `rsvpStatus` for specific `eventId`
- Guest projections: Calculate based on historical RSVP patterns

### Scalability Considerations

1. **Composite Indexes**: Create indexes for common query patterns
2. **Pagination**: Use cursor-based pagination for large guest lists
3. **Caching**: Implement client-side caching for frequently accessed data
4. **Batch Operations**: Use batch writes for bulk operations
5. **Cloud Functions**: Use for complex operations and data validation

### Migration Strategy

1. **Phase 1**: Create new collections alongside existing `invitees` collection
2. **Phase 2**: Migrate existing data to new structure
3. **Phase 3**: Update application code to use new structure
4. **Phase 4**: Remove old collection after verification

This architecture provides:

- ✅ Multi-tenant isolation
- ✅ Scalable event management
- ✅ Flexible collaboration system
- ✅ Subscription-based access control
- ✅ Comprehensive audit trail
- ✅ RTL language support ready
- ✅ Analytics and reporting capabilities
