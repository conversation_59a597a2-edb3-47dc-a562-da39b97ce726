import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  onSnapshot,
  writeBatch,
  serverTimestamp,
  Timestamp,
  type DocumentSnapshot,
  type QuerySnapshot,
  type Unsubscribe
} from 'firebase/firestore'
import { db } from '@/firebase'
import type {
  User,
  Event,
  Invitee,
  Collaborator,
  Subscription,
  Activity,
  CreateEventForm,
  CreateInviteeForm,
  InviteCollaboratorForm,
  QueryOptions,
  PaginatedResponse,
  ApiResponse
} from '@/types/firebase'

// Base Firebase Service Class
class FirebaseService {
  protected collectionName: string

  constructor(collectionName: string) {
    this.collectionName = collectionName
  }

  protected getCollection() {
    return collection(db, this.collectionName)
  }

  protected getDocRef(id: string) {
    return doc(db, this.collectionName, id)
  }

  protected async create<T>(data: Omit<T, 'id'>): Promise<string> {
    const docRef = await addDoc(this.getCollection(), {
      ...data,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    })
    return docRef.id
  }

  protected async read<T>(id: string): Promise<T | null> {
    const docSnap = await getDoc(this.getDocRef(id))
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as T
    }
    return null
  }

  protected async update<T>(id: string, data: Partial<T>): Promise<void> {
    await updateDoc(this.getDocRef(id), {
      ...data,
      updatedAt: serverTimestamp()
    })
  }

  protected async delete(id: string): Promise<void> {
    await deleteDoc(this.getDocRef(id))
  }

  protected async list<T>(options: QueryOptions = {}): Promise<PaginatedResponse<T>> {
    let q = query(this.getCollection())

    // Apply filters
    if (options.filters) {
      options.filters.forEach(filter => {
        q = query(q, where(filter.field, filter.operator, filter.value))
      })
    }

    // Apply ordering
    if (options.orderBy) {
      q = query(q, orderBy(options.orderBy, options.orderDirection || 'asc'))
    }

    // Apply pagination
    if (options.startAfter) {
      q = query(q, startAfter(options.startAfter))
    }

    if (options.limit) {
      q = query(q, limit(options.limit + 1)) // +1 to check if there are more items
    }

    const snapshot = await getDocs(q)
    const items: T[] = []
    
    snapshot.forEach((doc, index) => {
      if (!options.limit || index < options.limit) {
        items.push({ id: doc.id, ...doc.data() } as T)
      }
    })

    const hasMore = options.limit ? snapshot.size > options.limit : false
    const nextCursor = hasMore ? snapshot.docs[options.limit! - 1] : undefined

    return {
      items,
      total: snapshot.size,
      page: 1, // TODO: Implement proper page calculation
      limit: options.limit || snapshot.size,
      hasMore,
      nextCursor: nextCursor?.id
    }
  }

  protected subscribe<T>(
    callback: (items: T[]) => void,
    options: QueryOptions = {}
  ): Unsubscribe {
    let q = query(this.getCollection())

    // Apply filters
    if (options.filters) {
      options.filters.forEach(filter => {
        q = query(q, where(filter.field, filter.operator, filter.value))
      })
    }

    // Apply ordering
    if (options.orderBy) {
      q = query(q, orderBy(options.orderBy, options.orderDirection || 'asc'))
    }

    return onSnapshot(q, (snapshot) => {
      const items: T[] = []
      snapshot.forEach((doc) => {
        items.push({ id: doc.id, ...doc.data() } as T)
      })
      callback(items)
    })
  }
}

// User Service
export class UserService extends FirebaseService {
  constructor() {
    super('users')
  }

  async createUser(userId: string, userData: Partial<User>): Promise<void> {
    await updateDoc(this.getDocRef(userId), {
      ...userData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    })
  }

  async getUser(userId: string): Promise<User | null> {
    return this.read<User>(userId)
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<void> {
    return this.update<User>(userId, userData)
  }

  async updateUserPreferences(userId: string, preferences: Partial<User['preferences']>): Promise<void> {
    const user = await this.getUser(userId)
    if (user) {
      await this.updateUser(userId, {
        preferences: { ...user.preferences, ...preferences }
      })
    }
  }
}

// Event Service
export class EventService extends FirebaseService {
  constructor() {
    super('events')
  }

  async createEvent(ownerId: string, eventData: CreateEventForm): Promise<string> {
    const event: Omit<Event, 'id'> = {
      ownerId,
      name: eventData.name,
      description: eventData.description,
      type: eventData.type,
      date: Timestamp.fromDate(eventData.date),
      endDate: eventData.endDate ? Timestamp.fromDate(eventData.endDate) : undefined,
      venue: eventData.venue,
      settings: {
        allowGuestPlusOnes: false,
        requirePhoneNumber: false,
        requireAddress: false,
        customFields: [],
        invitationTemplate: 'default',
        ...eventData.settings
      },
      status: 'draft',
      privacy: 'private',
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
      metadata: {
        totalInvitees: 0,
        confirmedGuests: 0,
        pendingGuests: 0,
        declinedGuests: 0,
        lastActivity: serverTimestamp() as Timestamp
      }
    }

    return this.create<Event>(event)
  }

  async getUserEvents(userId: string): Promise<Event[]> {
    const result = await this.list<Event>({
      filters: [{ field: 'ownerId', operator: '==', value: userId }],
      orderBy: 'createdAt',
      orderDirection: 'desc'
    })
    return result.items
  }

  async getEvent(eventId: string): Promise<Event | null> {
    return this.read<Event>(eventId)
  }

  async updateEvent(eventId: string, eventData: Partial<Event>): Promise<void> {
    return this.update<Event>(eventId, eventData)
  }

  async deleteEvent(eventId: string): Promise<void> {
    // TODO: Implement cascade delete for related data (invitees, collaborators)
    return this.delete(eventId)
  }

  subscribeToUserEvents(userId: string, callback: (events: Event[]) => void): Unsubscribe {
    return this.subscribe<Event>(callback, {
      filters: [{ field: 'ownerId', operator: '==', value: userId }],
      orderBy: 'createdAt',
      orderDirection: 'desc'
    })
  }
}

// Invitee Service
export class InviteeService extends FirebaseService {
  constructor() {
    super('invitees')
  }

  async createInvitee(eventId: string, ownerId: string, inviteeData: CreateInviteeForm): Promise<string> {
    const invitee: Omit<Invitee, 'id'> = {
      eventId,
      ownerId,
      firstName: inviteeData.firstName,
      lastName: inviteeData.lastName,
      email: inviteeData.email,
      phoneNumber: inviteeData.phoneNumber,
      address: inviteeData.address as any,
      rsvpStatus: 'pending',
      guestCount: 0,
      maxGuests: inviteeData.maxGuests,
      plusOnes: [],
      dietaryRestrictions: inviteeData.dietaryRestrictions,
      notes: inviteeData.notes,
      invitedBy: ownerId,
      invitedAt: serverTimestamp() as Timestamp,
      tags: inviteeData.tags || [],
      metadata: {
        invitationsSent: 0,
        communicationHistory: []
      }
    }

    return this.create<Invitee>(invitee)
  }

  async getEventInvitees(eventId: string): Promise<Invitee[]> {
    const result = await this.list<Invitee>({
      filters: [{ field: 'eventId', operator: '==', value: eventId }],
      orderBy: 'createdAt',
      orderDirection: 'desc'
    })
    return result.items
  }

  async getInvitee(inviteeId: string): Promise<Invitee | null> {
    return this.read<Invitee>(inviteeId)
  }

  async updateInvitee(inviteeId: string, inviteeData: Partial<Invitee>): Promise<void> {
    return this.update<Invitee>(inviteeId, inviteeData)
  }

  async updateRSVP(inviteeId: string, rsvpStatus: Invitee['rsvpStatus'], guestCount: number): Promise<void> {
    return this.update<Invitee>(inviteeId, {
      rsvpStatus,
      guestCount,
      rsvpAt: serverTimestamp() as Timestamp
    })
  }

  async deleteInvitee(inviteeId: string): Promise<void> {
    return this.delete(inviteeId)
  }

  subscribeToEventInvitees(eventId: string, callback: (invitees: Invitee[]) => void): Unsubscribe {
    return this.subscribe<Invitee>(callback, {
      filters: [{ field: 'eventId', operator: '==', value: eventId }],
      orderBy: 'createdAt',
      orderDirection: 'desc'
    })
  }
}

// Service instances
export const userService = new UserService()
export const eventService = new EventService()
export const inviteeService = new InviteeService()

// Utility functions
export function convertTimestamp(timestamp: Timestamp): Date {
  return timestamp.toDate()
}

export function createTimestamp(date: Date): Timestamp {
  return Timestamp.fromDate(date)
}
