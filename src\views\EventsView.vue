<template>
  <AppLayout>
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('events.title') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            Manage your events and celebrations
          </p>
        </div>
        <Button
          :label="t('events.createEvent')"
          icon="pi pi-plus"
          @click="createEvent"
        />
      </div>
    </div>

    <Card>
      <template #content>
        <div class="text-center py-12">
          <i class="pi pi-calendar text-6xl text-surface-400 mb-4"></i>
          <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
            {{ t('events.noEvents') }}
          </h3>
          <p class="text-surface-600 dark:text-surface-400 mb-6">
            Create your first event to get started with guest management
          </p>
          <Button
            :label="t('events.createEvent')"
            icon="pi pi-plus"
            @click="createEvent"
          />
        </div>
      </template>
    </Card>
  </AppLayout>
</template>

<script setup lang="ts">
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'

const { t } = useLocale()

const createEvent = () => {
  console.log('Create event clicked')
  // TODO: Implement event creation
}
</script>
