<template>
  <AppLayout>
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('collaborators.title') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            Manage team members and permissions
          </p>
        </div>
        <Button
          :label="t('collaborators.inviteCollaborator')"
          icon="pi pi-user-plus"
          @click="inviteCollaborator"
        />
      </div>
    </div>

    <Card>
      <template #content>
        <div class="text-center py-12">
          <i class="pi pi-users text-6xl text-surface-400 mb-4"></i>
          <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
            No collaborators yet
          </h3>
          <p class="text-surface-600 dark:text-surface-400 mb-6">
            Invite team members to help manage your events
          </p>
          <Button
            :label="t('collaborators.inviteCollaborator')"
            icon="pi pi-user-plus"
            @click="inviteCollaborator"
          />
        </div>
      </template>
    </Card>
  </AppLayout>
</template>

<script setup lang="ts">
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'

const { t } = useLocale()

const inviteCollaborator = () => {
  console.log('Invite collaborator clicked')
}
</script>
