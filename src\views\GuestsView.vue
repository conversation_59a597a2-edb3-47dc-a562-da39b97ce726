<template>
  <AppLayout>
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('guests.title') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            Manage your guest list and RSVPs
          </p>
        </div>
        <div class="flex space-x-3 rtl:space-x-reverse">
          <Button
            :label="t('guests.importGuests')"
            icon="pi pi-upload"
            outlined
            @click="importGuests"
          />
          <Button
            :label="t('guests.addGuest')"
            icon="pi pi-plus"
            @click="addGuest"
          />
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <Card class="mb-6">
      <template #content>
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1">
            <InputText
              v-model="searchQuery"
              :placeholder="t('common.search') + ' ' + t('guests.title').toLowerCase() + '...'"
              class="w-full"
            />
          </div>
          <div class="flex space-x-3 rtl:space-x-reverse">
            <Select
              v-model="statusFilter"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              :placeholder="t('guests.rsvpStatus')"
              class="w-40"
            />
            <Button
              :label="t('guests.exportGuests')"
              icon="pi pi-download"
              outlined
              @click="exportGuests"
            />
          </div>
        </div>
      </template>
    </Card>

    <!-- Guests Table -->
    <Card>
      <template #content>
        <DataTable
          :value="guests"
          :paginator="true"
          :rows="10"
          :loading="loading"
          selection-mode="multiple"
          v-model:selection="selectedGuests"
          data-key="id"
          :global-filter-fields="['firstName', 'lastName', 'email', 'phone']"
          :global-filter="searchQuery"
        >
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg font-semibold">
                {{ guests.length }} {{ t('guests.title') }}
              </span>
              <div v-if="selectedGuests.length > 0" class="flex space-x-2 rtl:space-x-reverse">
                <Button
                  :label="`${t('common.delete')} (${selectedGuests.length})`"
                  icon="pi pi-trash"
                  severity="danger"
                  size="small"
                  @click="deleteSelected"
                />
              </div>
            </div>
          </template>

          <Column selection-mode="multiple" header-style="width: 3rem"></Column>
          
          <Column field="firstName" :header="t('guests.firstName')" sortable>
            <template #body="slotProps">
              <div class="flex items-center space-x-2 rtl:space-x-reverse">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                  <span class="text-sm font-semibold text-primary-600 dark:text-primary-400">
                    {{ slotProps.data.firstName.charAt(0) }}{{ slotProps.data.lastName.charAt(0) }}
                  </span>
                </div>
                <span>{{ slotProps.data.firstName }}</span>
              </div>
            </template>
          </Column>
          
          <Column field="lastName" :header="t('guests.lastName')" sortable></Column>
          <Column field="email" :header="t('guests.email')" sortable></Column>
          <Column field="phone" :header="t('guests.phone')" sortable></Column>
          
          <Column field="rsvpStatus" :header="t('guests.rsvpStatus')" sortable>
            <template #body="slotProps">
              <span
                :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  slotProps.data.rsvpStatus === 'confirmed' ? 'bg-green-100 text-green-800' :
                  slotProps.data.rsvpStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                ]"
              >
                {{ t(`guests.${slotProps.data.rsvpStatus}`) }}
              </span>
            </template>
          </Column>
          
          <Column :header="t('common.actions')">
            <template #body="slotProps">
              <div class="flex space-x-2 rtl:space-x-reverse">
                <Button
                  icon="pi pi-eye"
                  text
                  size="small"
                  @click="viewGuest(slotProps.data)"
                />
                <Button
                  icon="pi pi-pencil"
                  text
                  size="small"
                  @click="editGuest(slotProps.data)"
                />
                <Button
                  icon="pi pi-trash"
                  text
                  size="small"
                  severity="danger"
                  @click="deleteGuest(slotProps.data)"
                />
              </div>
            </template>
          </Column>

          <template #empty>
            <div class="text-center py-8">
              <i class="pi pi-users text-4xl text-surface-400 mb-4"></i>
              <p class="text-surface-600 dark:text-surface-400">
                {{ t('guests.noGuests') }}
              </p>
            </div>
          </template>
        </DataTable>
      </template>
    </Card>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'

const { t } = useLocale()

const searchQuery = ref('')
const statusFilter = ref('')
const selectedGuests = ref([])
const loading = ref(false)

// Mock data
const guests = ref([
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    rsvpStatus: 'confirmed'
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+1234567891',
    rsvpStatus: 'pending'
  },
  {
    id: '3',
    firstName: 'Bob',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+1234567892',
    rsvpStatus: 'declined'
  }
])

const statusOptions = computed(() => [
  { label: t('common.all'), value: '' },
  { label: t('guests.confirmed'), value: 'confirmed' },
  { label: t('guests.pending'), value: 'pending' },
  { label: t('guests.declined'), value: 'declined' }
])

const addGuest = () => {
  console.log('Add guest clicked')
}

const importGuests = () => {
  console.log('Import guests clicked')
}

const exportGuests = () => {
  console.log('Export guests clicked')
}

const viewGuest = (guest: any) => {
  console.log('View guest:', guest)
}

const editGuest = (guest: any) => {
  console.log('Edit guest:', guest)
}

const deleteGuest = (guest: any) => {
  console.log('Delete guest:', guest)
}

const deleteSelected = () => {
  console.log('Delete selected guests:', selectedGuests.value)
}
</script>
