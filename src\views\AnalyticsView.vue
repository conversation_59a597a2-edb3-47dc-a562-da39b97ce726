<template>
  <AppLayout>
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('analytics.title') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            Track your event performance and guest insights
          </p>
        </div>
        <Button
          :label="t('analytics.exportReport')"
          icon="pi pi-download"
          outlined
          @click="exportReport"
        />
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card class="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">85%</div>
              <div class="text-blue-100 text-sm">Response Rate</div>
            </div>
            <i class="pi pi-chart-line text-3xl text-blue-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-green-500 to-green-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">247</div>
              <div class="text-green-100 text-sm">Total Invitations</div>
            </div>
            <i class="pi pi-send text-3xl text-green-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">189</div>
              <div class="text-purple-100 text-sm">Confirmed Guests</div>
            </div>
            <i class="pi pi-check-circle text-3xl text-purple-200"></i>
          </div>
        </template>
      </Card>

      <Card class="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
        <template #content>
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold">12</div>
              <div class="text-orange-100 text-sm">Days Remaining</div>
            </div>
            <i class="pi pi-calendar text-3xl text-orange-200"></i>
          </div>
        </template>
      </Card>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- RSVP Trends -->
      <Card>
        <template #title>{{ t('analytics.rsvpTrends') }}</template>
        <template #content>
          <div class="h-64 flex items-center justify-center bg-surface-50 dark:bg-surface-800 rounded-lg">
            <div class="text-center">
              <i class="pi pi-chart-line text-4xl text-surface-400 mb-4"></i>
              <p class="text-surface-600 dark:text-surface-400">
                Chart will be implemented here
              </p>
            </div>
          </div>
        </template>
      </Card>

      <!-- Guest Projections -->
      <Card>
        <template #title>{{ t('analytics.guestProjections') }}</template>
        <template #content>
          <div class="h-64 flex items-center justify-center bg-surface-50 dark:bg-surface-800 rounded-lg">
            <div class="text-center">
              <i class="pi pi-chart-bar text-4xl text-surface-400 mb-4"></i>
              <p class="text-surface-600 dark:text-surface-400">
                Chart will be implemented here
              </p>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Detailed Analytics -->
    <Card>
      <template #title>{{ t('analytics.attendanceForecasting') }}</template>
      <template #content>
        <div class="space-y-6">
          <!-- Event Breakdown -->
          <div>
            <h4 class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-4">
              Event Breakdown
            </h4>
            <div class="space-y-4">
              <div class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg">
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                  <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <i class="pi pi-calendar text-blue-600 dark:text-blue-400"></i>
                  </div>
                  <div>
                    <h5 class="font-semibold text-surface-900 dark:text-surface-0">Wedding Ceremony</h5>
                    <p class="text-sm text-surface-600 dark:text-surface-400">Main event</p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-surface-900 dark:text-surface-0">150</div>
                  <div class="text-sm text-surface-600 dark:text-surface-400">Expected</div>
                </div>
              </div>

              <div class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg">
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                  <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <i class="pi pi-heart text-green-600 dark:text-green-400"></i>
                  </div>
                  <div>
                    <h5 class="font-semibold text-surface-900 dark:text-surface-0">Henna Night</h5>
                    <p class="text-sm text-surface-600 dark:text-surface-400">Pre-wedding celebration</p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-surface-900 dark:text-surface-0">80</div>
                  <div class="text-sm text-surface-600 dark:text-surface-400">Expected</div>
                </div>
              </div>

              <div class="flex items-center justify-between p-4 border border-surface-200 dark:border-surface-700 rounded-lg">
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                  <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <i class="pi pi-star text-purple-600 dark:text-purple-400"></i>
                  </div>
                  <div>
                    <h5 class="font-semibold text-surface-900 dark:text-surface-0">Reception</h5>
                    <p class="text-sm text-surface-600 dark:text-surface-400">Post-wedding party</p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-surface-900 dark:text-surface-0">200</div>
                  <div class="text-sm text-surface-600 dark:text-surface-400">Expected</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Card>
  </AppLayout>
</template>

<script setup lang="ts">
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'

const { t } = useLocale()

const exportReport = () => {
  console.log('Export report clicked')
  // TODO: Implement report export
}
</script>
