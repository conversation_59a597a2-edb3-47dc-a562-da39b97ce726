import { defineStore } from 'pinia'
import { eventService } from '@/services/firebase'
import type { Event, CreateEventForm } from '@/types/firebase'
import type { Unsubscribe } from 'firebase/firestore'
import { useAuthStore } from './auth'

interface EventsState {
  events: Event[]
  currentEvent: Event | null
  isLoading: boolean
  error: string | null
  unsubscribe: Unsubscribe | null
}

export const useEventsStore = defineStore('events', {
  state: (): EventsState => ({
    events: [],
    currentEvent: null,
    isLoading: false,
    error: null,
    unsubscribe: null
  }),

  getters: {
    getEventById: (state) => (id: string) => {
      return state.events.find(event => event.id === id) || null
    },

    upcomingEvents: (state) => {
      const now = new Date()
      return state.events.filter(event => event.date.toDate() > now)
    },

    pastEvents: (state) => {
      const now = new Date()
      return state.events.filter(event => event.date.toDate() <= now)
    },

    draftEvents: (state) => {
      return state.events.filter(event => event.status === 'draft')
    },

    publishedEvents: (state) => {
      return state.events.filter(event => event.status === 'published')
    },

    eventStats: (state) => {
      return {
        total: state.events.length,
        upcoming: state.events.filter(event => event.date.toDate() > new Date()).length,
        draft: state.events.filter(event => event.status === 'draft').length,
        published: state.events.filter(event => event.status === 'published').length
      }
    }
  },

  actions: {
    async loadUserEvents() {
      const authStore = useAuthStore()
      if (!authStore.user) {
        this.error = 'User not authenticated'
        return
      }

      this.isLoading = true
      this.error = null

      try {
        // Unsubscribe from previous listener
        if (this.unsubscribe) {
          this.unsubscribe()
        }

        // Set up real-time listener
        this.unsubscribe = eventService.subscribeToUserEvents(
          authStore.user.id,
          (events) => {
            this.events = events
            this.isLoading = false
          }
        )
      } catch (err: unknown) {
        console.error('Error loading user events:', err)
        this.error = err instanceof Error ? err.message : 'An unknown error occurred'
        this.isLoading = false
      }
    },

    async createEvent(eventData: CreateEventForm): Promise<string | null> {
      const authStore = useAuthStore()
      if (!authStore.user) {
        this.error = 'User not authenticated'
        return null
      }

      this.isLoading = true
      this.error = null

      try {
        const eventId = await eventService.createEvent(authStore.user.id, eventData)
        console.log('Event created with ID:', eventId)
        return eventId
      } catch (err: unknown) {
        console.error('Error creating event:', err)
        this.error = err instanceof Error ? err.message : 'An unknown error occurred'
        return null
      } finally {
        this.isLoading = false
      }
    },

    async loadEvent(eventId: string): Promise<Event | null> {
      this.isLoading = true
      this.error = null

      try {
        const event = await eventService.getEvent(eventId)
        if (event) {
          this.currentEvent = event
          // Also update in events array if it exists
          const index = this.events.findIndex(e => e.id === eventId)
          if (index !== -1) {
            this.events[index] = event
          }
        }
        return event
      } catch (err: unknown) {
        console.error('Error loading event:', err)
        this.error = err instanceof Error ? err.message : 'An unknown error occurred'
        return null
      } finally {
        this.isLoading = false
      }
    },

    async updateEvent(eventId: string, eventData: Partial<Event>): Promise<boolean> {
      this.isLoading = true
      this.error = null

      try {
        await eventService.updateEvent(eventId, eventData)
        
        // Update current event if it matches
        if (this.currentEvent && this.currentEvent.id === eventId) {
          this.currentEvent = { ...this.currentEvent, ...eventData }
        }

        // Update in events array
        const index = this.events.findIndex(e => e.id === eventId)
        if (index !== -1) {
          this.events[index] = { ...this.events[index], ...eventData }
        }

        return true
      } catch (err: unknown) {
        console.error('Error updating event:', err)
        this.error = err instanceof Error ? err.message : 'An unknown error occurred'
        return false
      } finally {
        this.isLoading = false
      }
    },

    async deleteEvent(eventId: string): Promise<boolean> {
      this.isLoading = true
      this.error = null

      try {
        await eventService.deleteEvent(eventId)
        
        // Remove from events array
        this.events = this.events.filter(e => e.id !== eventId)
        
        // Clear current event if it matches
        if (this.currentEvent && this.currentEvent.id === eventId) {
          this.currentEvent = null
        }

        return true
      } catch (err: unknown) {
        console.error('Error deleting event:', err)
        this.error = err instanceof Error ? err.message : 'An unknown error occurred'
        return false
      } finally {
        this.isLoading = false
      }
    },

    async publishEvent(eventId: string): Promise<boolean> {
      return this.updateEvent(eventId, { status: 'published' })
    },

    async unpublishEvent(eventId: string): Promise<boolean> {
      return this.updateEvent(eventId, { status: 'draft' })
    },

    setCurrentEvent(event: Event | null) {
      this.currentEvent = event
    },

    clearError() {
      this.error = null
    },

    cleanup() {
      if (this.unsubscribe) {
        this.unsubscribe()
        this.unsubscribe = null
      }
      this.events = []
      this.currentEvent = null
      this.error = null
    }
  }
})
