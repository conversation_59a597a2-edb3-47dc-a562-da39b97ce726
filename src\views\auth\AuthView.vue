<template>
  <div
    class="min-h-screen flex items-center justify-center bg-surface-50 dark:bg-surface-900 py-12 px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full">
      <!-- Logo/Brand Section -->
      <div class="text-center mb-8">
        <div
          class="mx-auto h-16 w-16 bg-primary-500 rounded-2xl flex items-center justify-center mb-4 shadow-lg"
        >
          <i class="pi pi-heart-fill text-white text-2xl"></i>
        </div>
        <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0 mb-2">Invity</h1>
        <h2 class="text-xl font-semibold text-surface-700 dark:text-surface-300">
          {{ isSignUp ? $t('auth.createAccount') : $t('auth.signInToAccount') }}
        </h2>
      </div>

      <!-- Auth Card -->
      <Card
        class="shadow-xl border border-surface-200 dark:border-surface-700 overflow-hidden bg-white dark:bg-surface-800"
      >
        <template #content>
          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Form Title with Transition -->
            <div class="text-center mb-6">
              <Transition name="slide-fade" mode="out-in">
                <h3
                  :key="isSignUp ? 'signup' : 'signin'"
                  class="text-lg font-semibold text-surface-900 dark:text-surface-0"
                >
                  {{ isSignUp ? $t('auth.createYourAccount') : $t('auth.welcomeBack') }}
                </h3>
              </Transition>
            </div>
            <!-- Name Fields (Sign Up Only) with Transition -->
            <Transition name="expand" mode="out-in">
              <div v-if="isSignUp" key="name-fields" class="grid grid-cols-2 gap-4">
                <!-- First Name -->
                <div>
                  <label
                    for="firstName"
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    {{ $t('auth.firstName') }}
                  </label>
                  <InputGroup>
                    <InputGroupAddon>
                      <i class="pi pi-user"></i>
                    </InputGroupAddon>
                    <InputText
                      id="firstName"
                      v-model="form.firstName"
                      :placeholder="$t('auth.firstNamePlaceholder')"
                      :invalid="!!errors.firstName"
                      class="w-full"
                      :required="isSignUp"
                    />
                  </InputGroup>
                  <small v-if="errors.firstName" class="text-red-500 mt-1 block">{{
                    errors.firstName
                  }}</small>
                </div>

                <!-- Last Name -->
                <div>
                  <label
                    for="lastName"
                    class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                  >
                    {{ $t('auth.lastName') }}
                  </label>
                  <InputGroup>
                    <InputGroupAddon class="bg-surface-100 dark:bg-surface-700">
                      <i class="pi pi-user text-surface-600 dark:text-surface-300"></i>
                    </InputGroupAddon>
                    <InputText
                      id="lastName"
                      v-model="form.lastName"
                      :placeholder="$t('auth.lastNamePlaceholder')"
                      :invalid="!!errors.lastName"
                      class="w-full"
                      :required="isSignUp"
                    />
                  </InputGroup>
                  <small v-if="errors.lastName" class="text-red-500 mt-1 block">{{
                    errors.lastName
                  }}</small>
                </div>
              </div>
            </Transition>

            <!-- Email -->
            <div>
              <label
                for="email"
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                {{ $t('auth.email') }}
              </label>
              <InputGroup>
                <InputGroupAddon class="bg-surface-100 dark:bg-surface-700">
                  <i class="pi pi-envelope text-surface-600 dark:text-surface-300"></i>
                </InputGroupAddon>
                <InputText
                  id="email"
                  v-model="form.email"
                  type="email"
                  :placeholder="$t('auth.emailPlaceholder')"
                  :invalid="!!errors.email"
                  class="w-full"
                  required
                />
              </InputGroup>
              <small v-if="errors.email" class="text-red-500 mt-1 block">{{ errors.email }}</small>
            </div>

            <!-- Password -->
            <div>
              <label
                for="password"
                class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
              >
                {{ $t('auth.password') }}
              </label>
              <InputGroup>
                <InputGroupAddon class="bg-surface-100 dark:bg-surface-700">
                  <i class="pi pi-lock text-surface-600 dark:text-surface-300"></i>
                </InputGroupAddon>
                <InputText
                  id="password"
                  v-model="form.password"
                  :type="showPassword ? 'text' : 'password'"
                  :placeholder="$t('auth.passwordPlaceholder')"
                  :invalid="!!errors.password"
                  class="w-full"
                  required
                />
                <InputGroupAddon class="bg-surface-100 dark:bg-surface-700">
                  <Button
                    @click="showPassword = !showPassword"
                    :icon="showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'"
                    text
                    size="small"
                    class="text-surface-600 dark:text-surface-300 hover:text-surface-800 dark:hover:text-surface-100"
                    type="button"
                  />
                </InputGroupAddon>
              </InputGroup>
              <small v-if="errors.password" class="text-red-500 mt-1 block">{{
                errors.password
              }}</small>
            </div>

            <!-- Confirm Password (Sign Up Only) with Transition -->
            <Transition name="expand" mode="out-in">
              <div v-if="isSignUp" key="confirm-password">
                <label
                  for="confirmPassword"
                  class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2"
                >
                  {{ $t('auth.confirmPassword') }}
                </label>
                <InputGroup>
                  <InputGroupAddon class="bg-surface-100 dark:bg-surface-700">
                    <i class="pi pi-lock text-surface-600 dark:text-surface-300"></i>
                  </InputGroupAddon>
                  <InputText
                    id="confirmPassword"
                    v-model="form.confirmPassword"
                    :type="showConfirmPassword ? 'text' : 'password'"
                    :placeholder="$t('auth.confirmPasswordPlaceholder')"
                    :invalid="!!errors.confirmPassword"
                    class="w-full"
                    :required="isSignUp"
                  />
                  <InputGroupAddon class="bg-surface-100 dark:bg-surface-700">
                    <Button
                      @click="showConfirmPassword = !showConfirmPassword"
                      :icon="showConfirmPassword ? 'pi pi-eye-slash' : 'pi pi-eye'"
                      text
                      size="small"
                      class="text-surface-600 dark:text-surface-300 hover:text-surface-800 dark:hover:text-surface-100"
                      type="button"
                    />
                  </InputGroupAddon>
                </InputGroup>
                <small v-if="errors.confirmPassword" class="text-red-500 mt-1 block">{{
                  errors.confirmPassword
                }}</small>
              </div>
            </Transition>

            <!-- Forgot Password Link (Sign In Only) -->
            <div v-if="!isSignUp" class="flex items-center justify-end">
              <router-link
                to="/forgot-password"
                class="text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 transition-colors"
              >
                {{ $t('auth.forgotPassword') }}
              </router-link>
            </div>

            <!-- Error Message -->
            <Message v-if="errorMessage" severity="error" :closable="false" class="mb-4">
              {{ errorMessage }}
            </Message>

            <!-- Email Verification Warning (Sign In Only) -->
            <Message
              v-if="!isSignUp && showEmailVerificationWarning"
              severity="warn"
              :closable="false"
              class="mb-4"
            >
              <div class="flex flex-col space-y-2">
                <span>{{ $t('auth.emailNotVerified') }}</span>
                <Button
                  @click="resendVerification"
                  :loading="isResendingVerification"
                  size="small"
                  text
                  class="self-start"
                >
                  {{ $t('auth.resendVerification') }}
                </Button>
              </div>
            </Message>

            <!-- Submit Button with Transition -->
            <Transition name="slide-fade" mode="out-in">
              <Button
                :key="isSignUp ? 'signup-btn' : 'signin-btn'"
                type="submit"
                :loading="authStore.isLoading"
                class="w-full"
                size="large"
                :disabled="authStore.isLoading"
              >
                <i :class="isSignUp ? 'pi pi-user-plus' : 'pi pi-sign-in'" class="mr-2"></i>
                {{ isSignUp ? $t('auth.createAccount') : $t('auth.signIn') }}
              </Button>
            </Transition>

            <!-- Toggle Auth Mode with Transition -->
            <div class="text-center">
              <Transition name="slide-fade" mode="out-in">
                <button
                  :key="isSignUp ? 'to-signin' : 'to-signup'"
                  type="button"
                  @click="toggleAuthMode"
                  class="text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 transition-colors"
                >
                  {{ isSignUp ? $t('auth.alreadyHaveAccount') : $t('auth.dontHaveAccount') }}
                  {{ isSignUp ? $t('auth.signIn') : $t('auth.createAccount') }}
                </button>
              </Transition>
            </div>

            <!-- Divider -->
            <div class="relative my-6">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-surface-200 dark:border-surface-600" />
              </div>
              <div class="relative flex justify-center text-sm">
                <span
                  class="px-4 bg-white dark:bg-surface-800 text-surface-500 dark:text-surface-400"
                >
                  {{ $t('auth.orContinueWith') }}
                </span>
              </div>
            </div>

            <!-- Google Sign In -->
            <Button
              type="button"
              @click="handleGoogleSignIn"
              :loading="authStore.isLoading"
              severity="secondary"
              outlined
              class="w-full"
              size="large"
              :disabled="authStore.isLoading"
            >
              <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              {{ $t('auth.continueWithGoogle') }}
            </Button>
          </form>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useI18n } from 'vue-i18n'
import Card from 'primevue/card'
import InputText from 'primevue/inputtext'
import InputGroup from 'primevue/inputgroup'
import InputGroupAddon from 'primevue/inputgroupaddon'
import Button from 'primevue/button'
import Message from 'primevue/message'
import { useToast } from 'primevue/usetoast'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const { t } = useI18n()
const toast = useToast()

// Auth mode state
const isSignUp = ref(false)

// Form state
const form = reactive({
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  confirmPassword: '',
})

const errors = ref<Record<string, string>>({})
const errorMessage = ref('')
const isResendingVerification = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// Check route to determine initial mode
onMounted(() => {
  if (route.path === '/register' || route.query.mode === 'signup') {
    isSignUp.value = true
  }
})

// Email verification warning
const showEmailVerificationWarning = computed(() => {
  return authStore.firebaseUser && !authStore.firebaseUser.emailVerified && !authStore.isLoading
})

// Toggle between sign in and sign up
const toggleAuthMode = () => {
  isSignUp.value = !isSignUp.value
  clearErrors()
  // Update URL without navigation
  const newPath = isSignUp.value ? '/register' : '/login'
  router.replace(newPath)
}

// Clear errors
const clearErrors = () => {
  errors.value = {}
  errorMessage.value = ''
}

// Validate form
const validateForm = () => {
  clearErrors()

  if (isSignUp.value) {
    if (!form.firstName.trim()) {
      errors.value.firstName = t('auth.errors.firstNameRequired')
    }
    if (!form.lastName.trim()) {
      errors.value.lastName = t('auth.errors.lastNameRequired')
    }
  }

  if (!form.email.trim()) {
    errors.value.email = t('auth.errors.emailRequired')
  } else if (!/\S+@\S+\.\S+/.test(form.email)) {
    errors.value.email = t('auth.errors.emailInvalid')
  }

  if (!form.password) {
    errors.value.password = t('auth.errors.passwordRequired')
  } else if (form.password.length < 6) {
    errors.value.password = t('auth.errors.passwordTooShort')
  }

  if (isSignUp.value) {
    if (!form.confirmPassword) {
      errors.value.confirmPassword = t('auth.errors.confirmPasswordRequired')
    } else if (form.password !== form.confirmPassword) {
      errors.value.confirmPassword = t('auth.errors.passwordsDoNotMatch')
    }
  }

  return Object.keys(errors.value).length === 0
}

// Handle form submission
const handleSubmit = async () => {
  if (!validateForm()) return

  try {
    if (isSignUp.value) {
      await authStore.register(form.email, form.password, form.firstName, form.lastName)
      toast.add({
        severity: 'success',
        summary: t('common.success'),
        detail: t('auth.verificationEmailSent'),
        life: 5000,
      })
    } else {
      await authStore.login(form.email, form.password)
    }

    router.push('/')
  } catch (error: any) {
    errorMessage.value = error.message || t('common.error')
  }
}

// Handle Google Sign In
const handleGoogleSignIn = async () => {
  try {
    await authStore.loginWithGoogle()
    router.push('/')
  } catch (error: any) {
    errorMessage.value = error.message || t('common.error')
  }
}

// Resend email verification
const resendVerification = async () => {
  if (!authStore.firebaseUser) return

  isResendingVerification.value = true
  try {
    await authStore.resendEmailVerification()
    toast.add({
      severity: 'success',
      summary: t('common.success'),
      detail: t('auth.verificationEmailSent'),
      life: 3000,
    })
  } catch (error: any) {
    toast.add({
      severity: 'error',
      summary: t('common.error'),
      detail: error.message,
      life: 3000,
    })
  } finally {
    isResendingVerification.value = false
  }
}
</script>

<style scoped>
/* Slide Fade Transition */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* Expand Transition for Form Fields */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: scaleY(0);
  transform-origin: top;
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 200px;
  transform: scaleY(1);
}
</style>
