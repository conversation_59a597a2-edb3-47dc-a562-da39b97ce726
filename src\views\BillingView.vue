<template>
  <AppLayout>
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('subscription.title') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            Manage your subscription and billing
          </p>
        </div>
        <Button
          :label="t('subscription.upgradePlan')"
          icon="pi pi-arrow-up"
          @click="upgradePlan"
        />
      </div>
    </div>

    <!-- Current Plan -->
    <Card class="mb-6">
      <template #title>{{ t('subscription.currentPlan') }}</template>
      <template #content>
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-2xl font-bold text-surface-900 dark:text-surface-0">Pro Plan</h3>
            <p class="text-surface-600 dark:text-surface-400">
              Up to 500 guests per event • Unlimited events • Advanced analytics
            </p>
          </div>
          <div class="text-right">
            <div class="text-3xl font-bold text-primary-600">$29</div>
            <div class="text-sm text-surface-600 dark:text-surface-400">per month</div>
          </div>
        </div>
      </template>
    </Card>

    <Card>
      <template #content>
        <div class="text-center py-12">
          <i class="pi pi-credit-card text-6xl text-surface-400 mb-4"></i>
          <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
            Billing management coming soon
          </h3>
          <p class="text-surface-600 dark:text-surface-400 mb-6">
            Full billing and subscription management features will be available here
          </p>
        </div>
      </template>
    </Card>
  </AppLayout>
</template>

<script setup lang="ts">
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'

const { t } = useLocale()

const upgradePlan = () => {
  console.log('Upgrade plan clicked')
}
</script>
