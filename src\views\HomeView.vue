<template>
  <div class="flex h-screen bg-gray-100 font-sans">
    <!-- Sidebar -->
    <aside class="w-64 bg-gray-800 text-gray-100 shadow-lg p-6 flex flex-col">
      <h1 class="text-2xl font-bold mb-8 text-center tracking-wide">Guest Manager</h1>
      <nav class="flex-grow">
        <ul>
          <li class="mb-2">
            <router-link to="/" class="flex items-center p-3 rounded-md text-base hover:bg-gray-700 transition duration-200 ease-in-out">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m0 0l7 7m-7-7v10a1 1 0 01-1 1H6"></path></svg>
              Dashboard
            </router-link>
          </li>
          <li class="mb-2">
            <button @click="currentInviteeId = null" class="w-full flex items-center p-3 rounded-md text-base hover:bg-gray-700 transition duration-200 ease-in-out">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
              Add New Guest
            </button>
          </li>
        </ul>
      </nav>
      <button @click="handleLogout" class="w-full bg-red-700 hover:bg-red-800 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path></svg>
        Logout
      </button>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-8 overflow-auto bg-gray-50 flex">
      <div class="flex-1 flex flex-col pr-8">
        <h2 class="text-3xl font-bold text-gray-800 mb-6">Guest List Overview <span class="text-gray-500 text-xl">({{ filteredInvitees.length }} total)</span></h2>

        <!-- Data Dashboard -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white p-6 rounded-lg shadow-md flex flex-col items-center justify-center">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">House Party</h3>
            <p class="text-3xl font-bold text-gray-800">{{ totalConfirmedGuestsByEvent.houseParty }}</p>
            <p class="text-gray-500 text-sm">Expected Guests</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md flex flex-col items-center justify-center">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Henna</h3>
            <p class="text-3xl font-bold text-gray-800">{{ totalConfirmedGuestsByEvent.henna }}</p>
            <p class="text-gray-500 text-sm">Expected Guests</p>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md flex flex-col items-center justify-center">
            <h3 class="text-lg font-semibold text-gray-700 mb-2">Wedding</h3>
            <p class="text-3xl font-bold text-gray-800">{{ totalConfirmedGuestsByEvent.wedding }}</p>
            <p class="text-gray-500 text-sm">Expected Guests</p>
          </div>
        </div>

        <!-- Search Bar and Add New Guest Button -->
        <div class="flex justify-between items-center mb-6">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search guests..."
            class="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-500 focus:border-gray-500 text-sm"
          />
          <div class="flex items-center ml-4">
            <input type="checkbox" id="hideCompleted" v-model="hideCompletedInvitees" class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500" />
            <label for="hideCompleted" class="ml-2 text-sm text-gray-700">Hide Completed</label>
          </div>
          <button v-if="selectedInvitees.length > 0" @click="bulkDeleteInvitees" class="ml-4 bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out">
            Delete Selected ({{ selectedInvitees.length }})
          </button>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md flex-1 overflow-y-auto">
          <!-- Guest Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border-collapse">
              <thead>
                <tr class="bg-gray-100 border-b border-gray-200">
                  <th class="py-2 px-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    <input type="checkbox" @change="toggleSelectAll" :checked="areAllInviteesSelected" class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500" />
                  </th>
                  <th class="py-2 px-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">First Name</th>
                  <th class="py-2 px-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Last Name</th>
                  <th class="py-2 px-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Phone Number</th>
                  <th class="py-2 px-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Guests</th>
                  <th class="py-2 px-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Events</th>
                  <th class="py-2 px-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="invitee in filteredInvitees" :key="invitee.id" class="border-b border-gray-100 hover:bg-gray-50 transition duration-150 ease-in-out">
                  <td class="py-2 px-3 text-sm text-gray-800">
                    <input type="checkbox" :value="invitee.id" v-model="selectedInvitees" class="h-4 w-4 text-gray-600 border-gray-300 rounded focus:ring-gray-500" />
                  </td>
                  <td class="py-2 px-3 text-sm text-gray-800">{{ invitee.firstName }}</td>
                  <td class="py-2 px-3 text-sm text-gray-800">{{ invitee.lastName }}</td>
                  <td class="py-2 px-3 text-sm text-gray-800">{{ invitee.phoneNumber }}</td>
                  <td class="py-2 px-3 text-sm text-gray-800">{{ invitee.confirmedGuests !== undefined && invitee.confirmedGuests !== null ? invitee.confirmedGuests : 'N/A' }}</td>
                  <td class="py-2 px-3 text-sm text-gray-800">
                    <template v-if="invitee.events?.houseParty || invitee.events?.henna || invitee.events?.wedding">
                      <span v-if="invitee.events?.houseParty" class="inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full mr-1">House Party</span>
                      <span v-if="invitee.events?.henna" class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full mr-1">Henna</span>
                      <span v-if="invitee.events?.wedding" class="inline-flex items-center bg-purple-100 text-purple-800 text-xs font-medium px-2 py-0.5 rounded-full">Wedding</span>
                    </template>
                    <template v-else>
                      N/A
                    </template>
                  </td>
                  <td class="py-2 px-3">
                    <button @click="currentInviteeId = invitee.id as string" class="text-gray-600 hover:text-gray-800 text-sm py-1 px-2 rounded-md transition duration-150 ease-in-out">
                      Edit
                    </button>
                    <button @click="deleteInvitee(invitee.id as string)" class="text-red-600 hover:text-red-800 text-sm py-1 px-2 rounded-md transition duration-150 ease-in-out ml-2">
                      Delete
                    </button>
                  </td>
                </tr>
                <tr v-if="filteredInvitees.length === 0">
                  <td colspan="6" class="py-4 text-center text-gray-500 text-sm">No guests found. Add some new guests to get started!</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Invitee Edit Panel -->
      <InviteeEditPanel :invitee-id="currentInviteeId" @saved="currentInviteeId = null" class="w-1/3" />
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useInviteesStore } from '../stores/invitees';

import InviteeEditPanel from '../components/InviteeEditPanel.vue';

const authStore = useAuthStore();
const inviteesStore = useInviteesStore();
const router = useRouter();

const searchQuery = ref('');
const currentInviteeId = ref<string | null>(null);
const selectedInvitees = ref<string[]>([]);
const hideCompletedInvitees = ref(false);

const filteredInvitees = computed(() => {
  return inviteesStore.invitees.filter((invitee) => {
    const firstName = invitee.firstName || '';
    const lastName = invitee.lastName || '';
    const phoneNumber = invitee.phoneNumber || '';

    const matchesSearch = firstName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                          lastName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                          phoneNumber.includes(searchQuery.value);

    const isCompleted = (invitee.confirmedGuests && invitee.confirmedGuests > 0) &&
                        (invitee.events?.houseParty || invitee.events?.henna || invitee.events?.wedding);

    if (hideCompletedInvitees.value && isCompleted) {
      return false;
    }

    return matchesSearch;
  });
});

const areAllInviteesSelected = computed(() => {
  return filteredInvitees.value.length > 0 && selectedInvitees.value.length === filteredInvitees.value.length;
});

const toggleSelectAll = () => {
  if (areAllInviteesSelected.value) {
    selectedInvitees.value = [];
  } else {
    selectedInvitees.value = filteredInvitees.value.map(invitee => invitee.id as string);
  }
};

const totalConfirmedGuestsByEvent = computed(() => inviteesStore.totalConfirmedGuestsByEvent);

onMounted(() => {
  inviteesStore.listenToInvitees();
});

watch(() => authStore.user, () => {
  inviteesStore.listenToInvitees();
});

const handleLogout = async () => {
  await authStore.logout();
  router.push('/login');
};

const deleteInvitee = async (id: string) => {
  if (confirm('Are you sure you want to delete this guest?')) {
    await inviteesStore.deleteInvitee(id);
    selectedInvitees.value = selectedInvitees.value.filter(selectedId => selectedId !== id);
  }
};

const bulkDeleteInvitees = async () => {
  if (selectedInvitees.value.length === 0) {
    alert('Please select at least one guest to delete.');
    return;
  }
  if (confirm(`Are you sure you want to delete ${selectedInvitees.value.length} selected guests?`)) {
    for (const id of selectedInvitees.value) {
      await inviteesStore.deleteInvitee(id);
    }
    selectedInvitees.value = []; // Clear selection after deletion
  }
};
</script>

<style scoped>
/* Add any component-specific styles here if needed */
</style>