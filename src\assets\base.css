/* Industrial/Professional Color Palette */
:root {
  --vt-c-white: #ffffff;
  --vt-c-light-gray: #f5f5f5;
  --vt-c-medium-gray: #e0e0e0;
  --vt-c-dark-gray: #333333;
  --vt-c-charcoal: #2c3e50;
  --vt-c-accent: #4a5568; /* A muted blue-gray for accents */

  --vt-c-divider: rgba(0, 0, 0, 0.1);
  --vt-c-divider-dark: rgba(255, 255, 255, 0.1);

  --vt-c-text-primary: var(--vt-c-dark-gray);
  --vt-c-text-secondary: #666666;
  --vt-c-text-inverted: var(--vt-c-white);
}

/* Semantic color variables for this project */
:root {
  --color-background: var(--vt-c-light-gray);
  --color-background-soft: var(--vt-c-white);
  --color-background-mute: var(--vt-c-medium-gray);

  --color-border: var(--vt-c-divider);
  --color-border-hover: rgba(0, 0, 0, 0.2);

  --color-heading: var(--vt-c-text-primary);
  --color-text: var(--vt-c-text-primary);
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-charcoal);
    --color-background-soft: #3b4a5b;
    --color-background-mute: #4a5b6c;

    --color-border: var(--vt-c-divider-dark);
    --color-border-hover: rgba(255, 255, 255, 0.2);

    --color-heading: var(--vt-c-text-inverted);
    --color-text: var(--vt-c-text-inverted);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
