import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'

const firebaseConfig = {
  apiKey: 'AIzaSyD5yq24s91s_IlTBhWwLN2erLYmz8MUtwU',
  authDomain: 'invity-ca3a5.firebaseapp.com',
  projectId: 'invity-ca3a5',
  storageBucket: 'invity-ca3a5.firebasestorage.app',
  messagingSenderId: '746899213991',
  appId: '1:746899213991:web:239fbe6408954692185582',
  measurementId: 'G-CCGEL2C3LV',
}

const app = initializeApp(firebaseConfig)

export const auth = getAuth(app)

export const db = getFirestore(app)
