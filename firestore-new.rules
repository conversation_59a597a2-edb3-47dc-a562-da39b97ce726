rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(ownerId) {
      return isAuthenticated() && request.auth.uid == ownerId;
    }
    
    function hasProjectAccess(projectId) {
      let projectDoc = get(/databases/$(database)/documents/projects/$(projectId));
      return projectDoc != null && isOwner(projectDoc.data.ownerId);
    }
    
    function isWithinProjectLimits(projectId) {
      let projectDoc = get(/databases/$(database)/documents/projects/$(projectId));
      
      if (projectDoc == null) {
        return false;
      }
      
      // Check if project is active
      if (projectDoc.data.status != 'active') {
        return false;
      }
      
      // For new events, check if within bundle limits
      if (request.method == 'create') {
        let currentEvents = projectDoc.data.metadata.totalEvents;
        return currentEvents < projectDoc.data.bundleLimits.maxEvents;
      }
      
      return true;
    }

    // Users collection
    match /users/{userId} {
      allow read, write: if isOwner(userId);
      allow create: if isAuthenticated() && request.auth.uid == userId;
    }

    // Projects collection
    match /projects/{projectId} {
      allow read, write: if isOwner(resource.data.ownerId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.ownerId);
      allow delete: if isOwner(resource.data.ownerId);
    }

    // Events collection
    match /events/{eventId} {
      allow read, write: if hasProjectAccess(resource.data.projectId);
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.ownerId) && 
                       hasProjectAccess(request.resource.data.projectId) &&
                       isWithinProjectLimits(request.resource.data.projectId);
      allow delete: if hasProjectAccess(resource.data.projectId);
    }

    // Guests collection
    match /guests/{guestId} {
      allow read, write: if hasProjectAccess(resource.data.projectId);
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.ownerId) && 
                       hasProjectAccess(request.resource.data.projectId);
      allow delete: if hasProjectAccess(resource.data.projectId);
    }

    // Payments collection
    match /payments/{paymentId} {
      allow read: if isOwner(resource.data.userId);
      allow create: if isAuthenticated() && isOwner(request.resource.data.userId);
      allow write, delete: if false; // Only backend/Stripe webhooks can modify payments
    }
  }
}
