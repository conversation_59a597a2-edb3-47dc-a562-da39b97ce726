import { defineStore } from 'pinia'
import { db } from '../firebase'
import {
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  onSnapshot,
  orderBy,
  limit,
  startAfter,
} from 'firebase/firestore'
import type { DocumentData } from 'firebase/firestore'
import { useAuthStore } from './auth'

export interface Invitee {
  id?: string
  firstName: string
  lastName: string
  phoneNumber: string
  confirmedGuests: number
  events: {
    houseParty: boolean
    henna: boolean
    wedding: boolean
  }
  invitedBy?: string
  invitedByUid?: string
}

interface InviteesState {
  invitees: Invitee[]
  isLoading: boolean
  error: string | null
  searchQuery: string
  filterByInviter: string
  lastDoc: DocumentData | null // For pagination
  hasMore: boolean
  unsubscribe: (() => void) | null // To store the unsubscribe function
}

export const useInviteesStore = defineStore('invitees', { // Added comment to force re-compilation
  state: (): InviteesState => ({
    invitees: [],
    isLoading: false,
    error: null,
    searchQuery: '',
    filterByInviter: '',
    lastDoc: null,
    hasMore: true,
    unsubscribe: null,
  }),
  getters: {
    filteredInvitees: (state) => {
      let filtered = state.invitees

      if (state.searchQuery) {
        const searchLower = state.searchQuery.toLowerCase()
        filtered = filtered.filter(
          (invitee) =>
            invitee.firstName.toLowerCase().includes(searchLower) ||
            invitee.lastName.toLowerCase().includes(searchLower) ||
            invitee.phoneNumber.includes(searchLower),
        )
      }

      if (state.filterByInviter) {
        filtered = filtered.filter((invitee) => invitee.invitedBy === state.filterByInviter)
      }

      return filtered
    },
    totalConfirmedGuestsByEvent: (state) => {
      const totals = {
        houseParty: 0,
        henna: 0,
        wedding: 0,
      }

      state.invitees.forEach((invitee) => {
        if (invitee.confirmedGuests > 0) {
          if (invitee.events?.houseParty) {
            totals.houseParty += invitee.confirmedGuests
          }
          if (invitee.events?.henna) {
            totals.henna += invitee.confirmedGuests
          }
          if (invitee.events?.wedding) {
            totals.wedding += invitee.confirmedGuests
          }
        }
      })
      return totals
    },
  },
  actions: {
    setSearchQuery(query: string) {
      this.searchQuery = query
    },
    setFilterByInviter(inviter: string) {
      this.filterByInviter = inviter
    },

    listenToInvitees() {
      const authStore = useAuthStore()

      // Unsubscribe from previous listener if exists
      if (this.unsubscribe) {
        this.unsubscribe()
        this.unsubscribe = null
      }

      this.isLoading = true
      this.error = null

      if (!authStore.user) {
        console.warn('Not authenticated. Cannot listen to invitees.')
        this.invitees = []
        this.isLoading = false
        return
      }

      const q = query(
        collection(db, 'invitees'),
        orderBy('lastName', 'asc'),
        orderBy('firstName', 'asc'),
      )

      this.unsubscribe = onSnapshot(
        q,
        (snapshot) => {
          const invitees: Invitee[] = []
          snapshot.forEach((doc) => {
            const data = doc.data()
            invitees.push({
              id: doc.id,
              firstName: data.firstName || '',
              lastName: data.lastName || '',
              phoneNumber: data.phoneNumber || '',
              confirmedGuests: data.confirmedGuests || 1,
              events: data.events || { houseParty: false, henna: false, wedding: false },
              invitedBy: data.invitedBy || '',
              invitedByUid: data.invitedByUid || '',
            } as Invitee)
          })
          this.invitees = invitees
          this.isLoading = false
          console.log('Invitees updated from Firestore via snapshot.', invitees.length)
        },
        (err: unknown) => {
          console.error('Error listening to invitees:', err)
          if (err instanceof Error) {
            this.error = err.message
          } else {
            this.error = 'An unknown error occurred.'
          }
          this.isLoading = false
        },
      )
    },

    async checkFirestoreData() {
      const authStore = useAuthStore()
      if (!authStore.user) {
        console.warn('Not authenticated. Cannot check Firestore data.')
        return 0
      }

      this.isLoading = true
      this.error = null
      try {
        const q = query(collection(db, 'invitees'))
        const snapshot = await getDocs(q)
        const count = snapshot.size
        console.log(`Firestore direct check found ${count} documents.`)
        return count
      } catch (err: unknown) {
        console.error('Error checking Firestore data:', err)
        if (err instanceof Error) {
          this.error = err.message
        }
        else {
          this.error = 'An unknown error occurred.'
        }
        return 0
      }
      finally {
        this.isLoading = false
      }
    },

    async addInvitee(invitee: Invitee) {
      const authStore = useAuthStore()
      if (!authStore.user) {
        throw new Error('User not authenticated.')
      }
      this.isLoading = true
      this.error = null
      try {
        const docRef = await addDoc(collection(db, 'invitees'), {
          ...invitee,
          invitedBy: authStore.user.displayName || authStore.user.email,
          invitedByUid: authStore.user.uid,
          createdAt: new Date(),
        })
        console.log('Invitee added with ID: ', docRef.id)
        // The snapshot listener will update the store's invitees array
      } catch (err: unknown) {
        console.error('Error adding invitee:', err)
        if (err instanceof Error) {
          this.error = err.message
        } else {
          this.error = 'An unknown error occurred.'
        }
        throw err
      } finally {
        this.isLoading = false
      }
    },

    async getInvitee(id: string) {
      this.isLoading = true
      this.error = null
      try {
        const docRef = doc(db, 'invitees', id)
        const docSnap = await getDoc(docRef)
        if (docSnap.exists()) {
          const data = docSnap.data()
          return {
            id: docSnap.id,
            firstName: data.firstName || '',
            lastName: data.lastName || '',
            phoneNumber: data.phoneNumber || '',
            confirmedGuests: data.confirmedGuests || 1,
            events: data.events || { houseParty: false, henna: false, wedding: false },
            invitedBy: data.invitedBy || '',
            invitedByUid: data.invitedByUid || '',
          } as Invitee
        } else {
          throw new Error('Invitee not found.')
        }
      } catch (err: unknown) {
        console.error('Error getting invitee:', err)
        if (err instanceof Error) {
          this.error = err.message
        } else {
          this.error = 'An unknown error occurred.'
        }
        throw err
      } finally {
        this.isLoading = false
      }
    },

    async updateInvitee(id: string, updates: Partial<Invitee>) {
      this.isLoading = true
      this.error = null
      try {
        const docRef = doc(db, 'invitees', id)
        await updateDoc(docRef, updates)
        console.log('Invitee updated: ', id)
        // The snapshot listener will update the store's invitees array
      } catch (err: unknown) {
        console.error('Error updating invitee:', err)
        if (err instanceof Error) {
          this.error = err.message
        } else {
          this.error = 'An unknown error occurred.'
        }
        throw err
      } finally {
        this.isLoading = false
      }
    },

    async deleteInvitee(id: string) {
      this.isLoading = true
      this.error = null
      try {
        const docRef = doc(db, 'invitees', id)
        await deleteDoc(docRef)
        console.log('Invitee deleted: ', id)
        // The snapshot listener will update the store's invitees array
      } catch (err: unknown) {
        console.error('Error deleting invitee:', err)
        if (err instanceof Error) {
          this.error = err.message
        } else {
          this.error = 'An unknown error occurred.'
        }
        throw err
      } finally {
        this.isLoading = false
      }
    },

    // Pagination (optional, for future use if needed)
    async fetchMoreInvitees() {
      const authStore = useAuthStore()
      if (!authStore.user || !this.hasMore) {
        return
      }

      this.isLoading = true
      this.error = null

      let q = query(
        collection(db, 'invitees'),
        orderBy('lastName', 'asc'),
        orderBy('firstName', 'asc'),
        limit(20), // Fetch 20 at a time
      )

      if (this.lastDoc) {
        q = query(q, startAfter(this.lastDoc))
      }

      try {
        const snapshot = await getDocs(q)
        if (snapshot.empty) {
          this.hasMore = false
          return
        }

        const newInvitees: Invitee[] = []
        snapshot.forEach((doc) => {
          const data = doc.data()
          newInvitees.push({
            id: doc.id,
            firstName: data.firstName || '',
            lastName: data.lastName || '',
            phoneNumber: data.phoneNumber || '',
            confirmedGuests: data.confirmedGuests || 1,
            events: data.events || { houseParty: false, henna: false, wedding: false },
            invitedBy: data.invitedBy || '',
            invitedByUid: data.invitedByUid || '',
          } as Invitee)
        })

        this.invitees = [...this.invitees, ...newInvitees]
        this.lastDoc = snapshot.docs[snapshot.docs.length - 1]
        this.hasMore = snapshot.docs.length === 20 // If less than 20, no more to fetch
      } catch (err: unknown) {
        console.error('Error fetching more invitees:', err)
        if (err instanceof Error) {
          this.error = err.message
        } else {
          this.error = 'An unknown error occurred.'
        }
      } finally {
        this.isLoading = false
      }
    },
  },
})
