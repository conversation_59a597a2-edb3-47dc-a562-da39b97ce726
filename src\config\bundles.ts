export interface BundleConfig {
  id: 'free' | 'basic' | 'premium' | 'ultimate'
  name: string
  price: number
  currency: string
  maxEvents: number
  maxGuestsPerEvent: number
  features: string[]
  popular?: boolean
}

export const BUNDLE_CONFIGS: Record<string, BundleConfig> = {
  free: {
    id: 'free',
    name: 'Free',
    price: 0,
    currency: 'USD',
    maxEvents: 1,
    maxGuestsPerEvent: 50,
    features: [
      '1 Project',
      '1 Event per project',
      'Up to 50 guests',
      'Basic guest management',
      'Phone duplicate detection'
    ]
  },
  basic: {
    id: 'basic',
    name: 'Basic Bundle',
    price: 29,
    currency: 'USD',
    maxEvents: 3,
    maxGuestsPerEvent: -1, // unlimited
    features: [
      'Unlimited projects',
      '3 Events per project',
      'Unlimited guests',
      'Advanced guest management',
      'Phone duplicate detection',
      'Guest projections',
      'Excel/CSV export'
    ]
  },
  premium: {
    id: 'premium',
    name: 'Premium Bundle',
    price: 49,
    currency: 'USD',
    maxEvents: 6,
    maxGuestsPerEvent: -1, // unlimited
    popular: true,
    features: [
      'Unlimited projects',
      '6 Events per project',
      'Unlimited guests',
      'Advanced guest management',
      'Phone duplicate detection',
      'Guest projections',
      'Excel/CSV export',
      'Priority support'
    ]
  },
  ultimate: {
    id: 'ultimate',
    name: 'Ultimate Bundle',
    price: 79,
    currency: 'USD',
    maxEvents: 12,
    maxGuestsPerEvent: -1, // unlimited
    features: [
      'Unlimited projects',
      '12 Events per project',
      'Unlimited guests',
      'Advanced guest management',
      'Phone duplicate detection',
      'Guest projections',
      'Excel/CSV export',
      'Priority support',
      'Custom event names',
      'Advanced analytics'
    ]
  }
}

export const getBundleConfig = (bundleId: string): BundleConfig => {
  return BUNDLE_CONFIGS[bundleId] || BUNDLE_CONFIGS.free
}
