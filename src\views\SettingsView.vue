<template>
  <AppLayout>
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            {{ t('navigation.settings') }}
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-1">
            Manage your account and preferences
          </p>
        </div>
      </div>
    </div>

    <!-- Settings Sections -->
    <div class="space-y-6">
      <!-- Profile Settings -->
      <Card>
        <template #title>Profile Settings</template>
        <template #content>
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                  First Name
                </label>
                <InputText v-model="profile.firstName" class="w-full" />
              </div>
              <div>
                <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                  Last Name
                </label>
                <InputText v-model="profile.lastName" class="w-full" />
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                Email
              </label>
              <InputText v-model="profile.email" class="w-full" />
            </div>
            <div class="flex justify-end">
              <Button :label="t('common.save')" @click="saveProfile" />
            </div>
          </div>
        </template>
      </Card>

      <!-- Language & Region -->
      <Card>
        <template #title>Language & Region</template>
        <template #content>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                Language
              </label>
              <Select
                v-model="selectedLocale"
                :options="availableLocales"
                option-label="name"
                option-value="code"
                @change="onLocaleChange"
                class="w-full md:w-64"
              >
                <template #value="slotProps">
                  <div class="flex items-center space-x-2 rtl:space-x-reverse">
                    <span>{{ getLocaleFlag(selectedLocale) }}</span>
                    <span>{{ getLocaleName(selectedLocale) }}</span>
                  </div>
                </template>
                <template #option="slotProps">
                  <div class="flex items-center space-x-2 rtl:space-x-reverse">
                    <span>{{ slotProps.option.flag }}</span>
                    <span>{{ slotProps.option.name }}</span>
                  </div>
                </template>
              </Select>
            </div>
          </div>
        </template>
      </Card>

      <!-- Notifications -->
      <Card>
        <template #title>Notifications</template>
        <template #content>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-surface-900 dark:text-surface-0">Email Notifications</h4>
                <p class="text-sm text-surface-600 dark:text-surface-400">
                  Receive email updates about your events
                </p>
              </div>
              <div class="text-center">
                <span class="text-sm text-surface-600 dark:text-surface-400">Coming Soon</span>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-surface-900 dark:text-surface-0">RSVP Notifications</h4>
                <p class="text-sm text-surface-600 dark:text-surface-400">
                  Get notified when guests respond to invitations
                </p>
              </div>
              <div class="text-center">
                <span class="text-sm text-surface-600 dark:text-surface-400">Coming Soon</span>
              </div>
            </div>
          </div>
        </template>
      </Card>

      <!-- Account Actions -->
      <Card>
        <template #title>Account</template>
        <template #content>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-surface-900 dark:text-surface-0">Change Password</h4>
                <p class="text-sm text-surface-600 dark:text-surface-400">
                  Update your account password
                </p>
              </div>
              <Button :label="t('common.change')" outlined @click="changePassword" />
            </div>
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-surface-900 dark:text-surface-0">Export Data</h4>
                <p class="text-sm text-surface-600 dark:text-surface-400">
                  Download all your event and guest data
                </p>
              </div>
              <Button :label="t('common.export')" outlined @click="exportData" />
            </div>
            <div class="flex items-center justify-between">
              <div>
                <h4 class="font-medium text-red-600">Delete Account</h4>
                <p class="text-sm text-surface-600 dark:text-surface-400">
                  Permanently delete your account and all data
                </p>
              </div>
              <Button :label="t('common.delete')" severity="danger" outlined @click="deleteAccount" />
            </div>
          </div>
        </template>
      </Card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useLocale } from '@/composables/useLocale'
import AppLayout from '@/components/layout/AppLayout.vue'
import Card from 'primevue/card'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'

const { locale, availableLocales, changeLocale, t } = useLocale()

const selectedLocale = ref(locale.value)

const profile = ref({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>'
})

const onLocaleChange = () => {
  changeLocale(selectedLocale.value)
}

const getLocaleFlag = (code: string) => {
  const locale = availableLocales.find(l => l.code === code)
  return locale?.flag || '🌐'
}

const getLocaleName = (code: string) => {
  const locale = availableLocales.find(l => l.code === code)
  return locale?.name || code
}

const saveProfile = () => {
  console.log('Save profile clicked')
}

const changePassword = () => {
  console.log('Change password clicked')
}

const exportData = () => {
  console.log('Export data clicked')
}

const deleteAccount = () => {
  console.log('Delete account clicked')
}
</script>
