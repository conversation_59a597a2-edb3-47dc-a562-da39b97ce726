export interface ProjectTier {
  id: string
  name: string
  price: number
  currency: string
  features: {
    maxEvents: number // Events within this project
    maxCollaborators: number
    maxGuests: number | 'unlimited'
    dataRetentionWeeks: number // Post-event data retention
    hasProjections: boolean // Guest count projections
    hasAdvancedAnalytics: boolean
    hasCustomBranding: boolean
    hasPrioritySupport: boolean
    hasManualTotalsOnly: boolean // Free tier limitation
  }
  popular?: boolean
  description: string
  stripePriceId?: string // For future Stripe integration
  upgradeFrom?: string[] // Which tiers can upgrade to this one
}

export const PROJECT_TIERS: Record<string, ProjectTier> = {
  free: {
    id: 'free',
    name: 'Free',
    price: 0,
    currency: 'USD',
    description: 'Perfect for small, simple events',
    features: {
      maxEvents: 1,
      maxCollaborators: 0,
      maxGuests: 50,
      dataRetentionWeeks: 1, // 1 week post-event
      hasProjections: false,
      hasAdvancedAnalytics: false,
      hasCustomBranding: false,
      hasPrioritySupport: false,
      hasManualTotalsOnly: true, // Only manual totals, no projections
    },
    upgradeFrom: [],
  },
  basic: {
    id: 'basic',
    name: 'Basic',
    price: 29,
    currency: 'USD',
    description: 'Great for medium-sized events with basic collaboration',
    features: {
      maxEvents: 1,
      maxCollaborators: 2,
      maxGuests: 150,
      dataRetentionWeeks: 4, // 1 month post-event
      hasProjections: false,
      hasAdvancedAnalytics: false,
      hasCustomBranding: false,
      hasPrioritySupport: false,
      hasManualTotalsOnly: true,
    },
    upgradeFrom: ['free'],
  },
  premium: {
    id: 'premium',
    name: 'Premium',
    price: 59,
    currency: 'USD',
    description: 'Perfect for multiple events with advanced features',
    popular: true,
    features: {
      maxEvents: 2,
      maxCollaborators: 5,
      maxGuests: 300,
      dataRetentionWeeks: 8, // 2 months post-event
      hasProjections: true,
      hasAdvancedAnalytics: true,
      hasCustomBranding: false,
      hasPrioritySupport: false,
      hasManualTotalsOnly: false, // Has projections
    },
    upgradeFrom: ['free', 'basic'],
  },
  gold: {
    id: 'gold',
    name: 'Gold',
    price: 99,
    currency: 'USD',
    description: 'Ultimate package for professional event planners',
    features: {
      maxEvents: 5,
      maxCollaborators: 10,
      maxGuests: 'unlimited',
      dataRetentionWeeks: 16, // 4 months post-event
      hasProjections: true,
      hasAdvancedAnalytics: true,
      hasCustomBranding: true,
      hasPrioritySupport: true,
      hasManualTotalsOnly: false,
    },
    upgradeFrom: ['free', 'basic', 'premium'],
  },
}

// Helper functions for project tier management
export class ProjectTierManager {
  static getTier(tierId: string): ProjectTier | null {
    return PROJECT_TIERS[tierId] || null
  }

  static getAllTiers(): ProjectTier[] {
    return Object.values(PROJECT_TIERS)
  }

  static canCreateEvent(projectTier: string, currentEventCount: number): boolean {
    const tier = this.getTier(projectTier)
    if (!tier) return false
    return currentEventCount < tier.features.maxEvents
  }

  static canAddCollaborator(projectTier: string, currentCollaboratorCount: number): boolean {
    const tier = this.getTier(projectTier)
    if (!tier) return false
    return currentCollaboratorCount < tier.features.maxCollaborators
  }

  static canAddGuest(projectTier: string, currentGuestCount: number): boolean {
    const tier = this.getTier(projectTier)
    if (!tier) return false
    if (tier.features.maxGuests === 'unlimited') return true
    return currentGuestCount < (tier.features.maxGuests as number)
  }

  static getDataRetentionDate(projectTier: string, eventDate: Date): Date {
    const tier = this.getTier(projectTier)
    if (!tier) return new Date()

    const retentionDate = new Date(eventDate)
    retentionDate.setDate(retentionDate.getDate() + tier.features.dataRetentionWeeks * 7)
    return retentionDate
  }

  static hasFeature(projectTier: string, feature: keyof ProjectTier['features']): boolean {
    const tier = this.getTier(projectTier)
    if (!tier) return false
    return Boolean(tier.features[feature])
  }

  static getUpgradeOptions(currentTier: string): ProjectTier[] {
    const current = this.getTier(currentTier)
    if (!current) return []

    return this.getAllTiers()
      .filter((tier) => tier.price > current.price && tier.upgradeFrom?.includes(currentTier))
      .sort((a, b) => a.price - b.price)
  }

  static getUpgradeRecommendation(
    currentTier: string,
    reason: 'events' | 'collaborators' | 'guests' | 'retention' | 'projections',
  ): ProjectTier | null {
    const current = this.getTier(currentTier)
    if (!current) return null

    const upgradeOptions = this.getUpgradeOptions(currentTier)

    for (const tier of upgradeOptions) {
      switch (reason) {
        case 'events':
          if (tier.features.maxEvents > current.features.maxEvents) return tier
          break
        case 'collaborators':
          if (tier.features.maxCollaborators > current.features.maxCollaborators) return tier
          break
        case 'guests':
          if (
            tier.features.maxGuests === 'unlimited' ||
            (typeof tier.features.maxGuests === 'number' &&
              typeof current.features.maxGuests === 'number' &&
              tier.features.maxGuests > current.features.maxGuests)
          )
            return tier
          break
        case 'retention':
          if (tier.features.dataRetentionWeeks > current.features.dataRetentionWeeks) return tier
          break
        case 'projections':
          if (tier.features.hasProjections && !current.features.hasProjections) return tier
          break
      }
    }

    return null
  }

  static formatPrice(tier: ProjectTier): string {
    if (tier.price === 0) return 'Free'
    return `$${tier.price}`
  }

  static formatGuestLimit(maxGuests: number | 'unlimited'): string {
    if (maxGuests === 'unlimited') return 'Unlimited'
    return maxGuests.toString()
  }

  static formatRetention(weeks: number): string {
    if (weeks < 4) return `${weeks} week${weeks === 1 ? '' : 's'}`
    if (weeks < 52) return `${Math.floor(weeks / 4)} month${Math.floor(weeks / 4) === 1 ? '' : 's'}`
    return `${Math.floor(weeks / 52)} year${Math.floor(weeks / 52) === 1 ? '' : 's'}`
  }
}

// Default tier for new users
export const DEFAULT_TIER = 'free'

// Tier upgrade paths (for UI flow)
export const TIER_UPGRADE_PATHS: Record<string, string[]> = {
  free: ['basic', 'premium', 'gold'],
  basic: ['premium', 'gold'],
  premium: ['gold'],
  gold: [],
}
