import type { Timestamp } from 'firebase/firestore'

// User Management Types
export interface User {
  id: string
  email: string
  displayName: string
  firstName: string
  lastName: string
  phoneNumber?: string
  profilePicture?: string
  createdAt: Timestamp
  updatedAt: Timestamp
  lastLoginAt: Timestamp
  preferences: UserPreferences
  metadata: UserMetadata
}

export interface UserPreferences {
  language: 'en' | 'ar' | 'he'
  timezone: string
  theme: 'light' | 'dark' | 'auto'
}

export interface UserMetadata {
  totalProjects: number
  totalGuests: number
  freeProjectUsed: boolean
}

// Project Management Types
export interface Project {
  id: string
  ownerId: string
  name: string
  description?: string
  bundle: 'free' | 'basic' | 'premium' | 'ultimate'
  bundleLimits: BundleLimits
  paymentId?: string
  status: 'active' | 'expired'
  expiresAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
  metadata: ProjectMetadata
}

export interface BundleLimits {
  maxEvents: number
  maxGuestsPerEvent: number
}

export interface ProjectMetadata {
  totalEvents: number
  totalGuests: number
  lastActivity: Timestamp
}

// Event Management Types
export interface Event {
  id: string
  projectId: string
  ownerId: string
  name: string
  description?: string
  date: Timestamp
  venue?: Venue
  status: 'active' | 'completed'
  createdAt: Timestamp
  updatedAt: Timestamp
  metadata: EventMetadata
}

export interface Venue {
  name: string
  address: string
  city: string
}

export interface EventMetadata {
  totalGuests: number
  expectedGuests: number
  guestsWithExpectations: number
  lastActivity: Timestamp
}

// Guest Management Types
export interface Guest {
  id: string
  projectId: string
  ownerId: string
  firstName: string
  lastName: string
  phoneNumber: string
  notes?: string
  expectedGuests: { [eventId: string]: number }
  addedBy: string
  addedAt: Timestamp
  updatedAt: Timestamp
  metadata: GuestMetadata
}

export interface GuestMetadata {
  totalExpectedAcrossEvents: number
  eventsInvitedTo: string[]
}

// Payment Types
export interface Payment {
  id: string
  userId: string
  projectId: string
  bundle: Project['bundle']
  amount: number
  currency: string
  stripePaymentIntentId: string
  status: 'pending' | 'succeeded' | 'failed' | 'cancelled'
  createdAt: Timestamp
  completedAt?: Timestamp
}

// Activity/Audit Types
export interface Activity {
  id: string
  eventId?: string
  userId: string
  type: ActivityType
  description: string
  metadata: { [key: string]: any }
  ipAddress?: string
  userAgent?: string
  createdAt: Timestamp
}

export type ActivityType =
  | 'event_created'
  | 'event_updated'
  | 'event_deleted'
  | 'guest_added'
  | 'guest_updated'
  | 'guest_deleted'
  | 'rsvp_received'
  | 'collaborator_invited'
  | 'collaborator_accepted'
  | 'collaborator_removed'
  | 'invitation_sent'
  | 'reminder_sent'
  | 'subscription_updated'
  | 'user_login'
  | 'user_logout'

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
  nextCursor?: string
}

// Query Types
export interface QueryOptions {
  limit?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
  startAfter?: any
  filters?: QueryFilter[]
}

export interface QueryFilter {
  field: string
  operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains'
  value: any
}

// Form Types for UI
export interface CreateProjectForm {
  name: string
  description?: string
  bundle: Project['bundle']
}

export interface CreateEventForm {
  name: string
  description?: string
  date: Date
  venue?: Venue
}

export interface CreateGuestForm {
  firstName: string
  lastName: string
  phoneNumber: string
  notes?: string
  expectedGuests?: { [eventId: string]: number }
}
