@import 'tailwindcss';
@import 'tailwindcss-primeui';
@import 'primeicons/primeicons.css';

/* RTL Support Styles */
[dir='rtl'] {
  direction: rtl;
}

[dir='ltr'] {
  direction: ltr;
}

/* RTL-specific utility classes */
.rtl-flip {
  transform: scaleX(-1);
}

[dir='rtl'] .rtl-flip {
  transform: scaleX(1);
}

/* RTL margin and padding utilities */
[dir='rtl'] .ml-auto {
  margin-left: unset;
  margin-right: auto;
}

[dir='rtl'] .mr-auto {
  margin-right: unset;
  margin-left: auto;
}

[dir='rtl'] .text-left {
  text-align: right;
}

[dir='rtl'] .text-right {
  text-align: left;
}

/* PrimeVue RTL Support */
[dir='rtl'] .p-dropdown-panel {
  left: auto !important;
  right: 0 !important;
}

[dir='rtl'] .p-menu {
  left: auto !important;
  right: 0 !important;
}

[dir='rtl'] .p-tooltip {
  left: auto !important;
  right: 0 !important;
}

/* Custom RTL animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

[dir='rtl'] .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

[dir='ltr'] .slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* Ensure proper font rendering for Arabic and Hebrew */
[lang='ar'],
[lang='he'] {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Fix for PrimeVue components in RTL */
[dir='rtl'] .p-inputtext {
  text-align: right;
}

[dir='rtl'] .p-button .p-button-icon-left {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir='rtl'] .p-button .p-button-icon-right {
  margin-left: 0;
  margin-right: 0.5rem;
}
