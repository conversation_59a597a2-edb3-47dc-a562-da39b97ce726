import {
  doc,
  collection,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  type Timestamp,
} from 'firebase/firestore'
import { db } from '@/firebase'
import type { WeddingProject } from '@/types/user'
import { UserService } from './userService'
import { ProjectTierManager, PROJECT_TIERS } from '@/config/subscriptionTiers'

export interface CreateProjectData {
  name: string
  weddingDate: Date
  venue?: string
  description?: string
  expectedGuests: number
  tier: string
}

export class ProjectService {
  /**
   * Create a new wedding project
   */
  static async createProject(
    userId: string,
    projectData: CreateProjectData,
  ): Promise<WeddingProject> {
    try {
      // Validate tier
      const tier = PROJECT_TIERS[projectData.tier]
      if (!tier) {
        throw new Error('Invalid tier selected')
      }

      // Validate guest count against tier limits
      if (
        tier.features.maxGuests !== 'unlimited' &&
        typeof tier.features.maxGuests === 'number' &&
        projectData.expectedGuests > tier.features.maxGuests
      ) {
        throw new Error(`Guest count exceeds tier limit of ${tier.features.maxGuests}`)
      }

      // Create project document
      const projectRef = collection(db, 'projects')
      const projectDoc: Omit<WeddingProject, 'id'> = {
        name: projectData.name,
        weddingDate: {
          seconds: Math.floor(projectData.weddingDate.getTime() / 1000),
          nanoseconds: 0,
        } as Timestamp,
        venue: projectData.venue,
        description: projectData.description,
        expectedGuests: projectData.expectedGuests,

        // Tier information
        tier: projectData.tier,
        tierPurchasedAt: serverTimestamp() as Timestamp,
        tierExpiresAt: tier.features.dataRetentionWeeks
          ? ({
              seconds: Math.floor(
                (Date.now() + tier.features.dataRetentionWeeks * 7 * 24 * 60 * 60 * 1000) / 1000,
              ),
              nanoseconds: 0,
            } as Timestamp)
          : undefined,

        // Payment information
        paymentStatus: tier.price === 0 ? 'free' : 'pending',
        amountPaid: tier.price,
        currency: 'USD',

        // Usage tracking
        usage: {
          eventsCreated: 0,
          currentEvents: 0,
          totalGuests: 0,
          collaboratorsAdded: 0,
        },

        // Collaboration
        collaborators: [userId], // Owner is first collaborator

        // Status
        status: 'planning',

        // Metadata
        createdAt: serverTimestamp() as Timestamp,
        updatedAt: serverTimestamp() as Timestamp,
        createdBy: userId,
      }

      const docRef = await addDoc(projectRef, projectDoc)

      // Add project to user's project list
      await UserService.addProjectToUser(userId, docRef.id)

      // Return the created project with ID
      return {
        id: docRef.id,
        ...projectDoc,
        // Convert serverTimestamp to actual timestamp for return
        tierPurchasedAt: { seconds: Math.floor(Date.now() / 1000), nanoseconds: 0 } as Timestamp,
        createdAt: { seconds: Math.floor(Date.now() / 1000), nanoseconds: 0 } as Timestamp,
        updatedAt: { seconds: Math.floor(Date.now() / 1000), nanoseconds: 0 } as Timestamp,
      }
    } catch (error) {
      console.error('Error creating project:', error)
      throw error
    }
  }

  /**
   * Get project by ID
   */
  static async getProject(projectId: string): Promise<WeddingProject | null> {
    try {
      const projectRef = doc(db, 'projects', projectId)
      const projectSnap = await getDoc(projectRef)

      if (projectSnap.exists()) {
        return {
          id: projectSnap.id,
          ...projectSnap.data(),
        } as WeddingProject
      }

      return null
    } catch (error) {
      console.error('Error getting project:', error)
      return null
    }
  }

  /**
   * Get all projects for a user
   */
  static async getUserProjects(userId: string): Promise<WeddingProject[]> {
    try {
      const projectsRef = collection(db, 'projects')
      const q = query(
        projectsRef,
        where('collaborators', 'array-contains', userId),
        orderBy('createdAt', 'desc'),
      )

      const querySnapshot = await getDocs(q)
      const projects: WeddingProject[] = []

      querySnapshot.forEach((doc) => {
        projects.push({
          id: doc.id,
          ...doc.data(),
        } as WeddingProject)
      })

      return projects
    } catch (error) {
      console.error('Error getting user projects:', error)
      return []
    }
  }

  /**
   * Update project
   */
  static async updateProject(projectId: string, updates: Partial<WeddingProject>): Promise<void> {
    try {
      const projectRef = doc(db, 'projects', projectId)
      await updateDoc(projectRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      })
    } catch (error) {
      console.error('Error updating project:', error)
      throw error
    }
  }

  /**
   * Delete project
   */
  static async deleteProject(projectId: string, userId: string): Promise<void> {
    try {
      // Remove project from user's project list
      await UserService.removeProjectFromUser(userId, projectId)

      // Delete the project document
      const projectRef = doc(db, 'projects', projectId)
      await deleteDoc(projectRef)
    } catch (error) {
      console.error('Error deleting project:', error)
      throw error
    }
  }

  /**
   * Upgrade project tier
   */
  static async upgradeProjectTier(projectId: string, newTier: string): Promise<void> {
    try {
      const tier = PROJECT_TIERS[newTier]
      if (!tier) {
        throw new Error('Invalid tier selected')
      }

      const updates: Partial<WeddingProject> = {
        tier: newTier,
        tierPurchasedAt: serverTimestamp() as Timestamp,
        tierExpiresAt: tier.features.dataRetentionWeeks
          ? ({
              seconds: Math.floor(
                (Date.now() + tier.features.dataRetentionWeeks * 7 * 24 * 60 * 60 * 1000) / 1000,
              ),
              nanoseconds: 0,
            } as Timestamp)
          : undefined,
        paymentStatus: tier.price === 0 ? 'free' : 'pending',
        amountPaid: tier.price,
        currency: 'USD',
      }

      await this.updateProject(projectId, updates)
    } catch (error) {
      console.error('Error upgrading project tier:', error)
      throw error
    }
  }

  /**
   * Add collaborator to project
   */
  static async addCollaborator(projectId: string, userId: string): Promise<void> {
    try {
      const project = await this.getProject(projectId)
      if (!project) {
        throw new Error('Project not found')
      }

      if (!project.collaborators.includes(userId)) {
        const updatedCollaborators = [...project.collaborators, userId]
        const updatedUsage = {
          ...project.usage,
          collaboratorsAdded: project.usage.collaboratorsAdded + 1,
        }
        await this.updateProject(projectId, {
          collaborators: updatedCollaborators,
          usage: updatedUsage,
        })

        // Add project to user's project list
        await UserService.addProjectToUser(userId, projectId)
      }
    } catch (error) {
      console.error('Error adding collaborator:', error)
      throw error
    }
  }

  /**
   * Remove collaborator from project
   */
  static async removeCollaborator(projectId: string, userId: string): Promise<void> {
    try {
      const project = await this.getProject(projectId)
      if (!project) {
        throw new Error('Project not found')
      }

      const updatedCollaborators = project.collaborators.filter((id) => id !== userId)
      await this.updateProject(projectId, { collaborators: updatedCollaborators })

      // Remove project from user's project list
      await UserService.removeProjectFromUser(userId, projectId)
    } catch (error) {
      console.error('Error removing collaborator:', error)
      throw error
    }
  }
}
